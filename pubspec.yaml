name: ds_admin
description: "admin pannel for dabgar samaj"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+7

environment:
  sdk: ^3.6.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  freezed_annotation: ^2.4.4
  json_annotation: ^4.9.0
  injectable: ^2.5.0
  get_it: ^8.0.3
  intl:
  url_strategy: ^0.3.0
  easy_localization: ^3.0.7+1
  easy_localization_loader: ^2.0.2

  #Local Database
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  #State Management
  flutter_riverpod: ^2.6.1

  #UI
  flutter_screenutil: ^5.9.3
  responsive_builder: ^0.7.1
  shimmer: ^3.0.0
  flutter_launcher_icons: ^0.14.3
  loading_animation_widget: ^1.3.0
  flutter_svg: ^2.0.17
  animated_text_kit: ^4.2.3
  fl_chart: ^0.70.2
  data_table_2: ^2.5.18
  html_editor_enhanced: ^2.7.1
  image_picker_web: ^4.0.0
  html: ^0.15.5
  mime_type: ^1.0.1
  flutter_widget_from_html: ^0.16.0
  dropdown_button2: ^2.3.9
  transliteration: ^0.0.6

  #Network
  dio: ^5.8.0+1

dependency_overrides:
  lints: ^4.0.0
  http: ^0.13.1


dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  build_runner: ^2.4.13
  freezed: ^2.5.7
  json_serializable: ^6.9.0
  injectable_generator: ^2.6.2
  flutter_gen_runner: ^5.8.0
  lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec
# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/fonts/
    - lang/en.json
    - lang/gu.json
    - lang/hi.json

  fonts:
    - family: Poppins
      fonts:
        - asset: assets/fonts/Poppins-Black.ttf
        - asset: assets/fonts/Poppins-BlackItalic.ttf
        - asset: assets/fonts/Poppins-Bold.ttf
        - asset: assets/fonts/Poppins-BoldItalic.ttf
        - asset: assets/fonts/Poppins-ExtraBoldItalic.ttf
        - asset: assets/fonts/Poppins-ExtraLight.ttf
        - asset: assets/fonts/Poppins-ExtraLightItalic.ttf
        - asset: assets/fonts/Poppins-Italic.ttf
        - asset: assets/fonts/Poppins-Light.ttf
        - asset: assets/fonts/Poppins-LightItalic.ttf
        - asset: assets/fonts/Poppins-Medium.ttf
        - asset: assets/fonts/Poppins-MediumItalic.ttf
        - asset: assets/fonts/Poppins-Regular.ttf
        - asset: assets/fonts/Poppins-SemiBold.ttf
        - asset: assets/fonts/Poppins-SemiBoldItalic.ttf
        - asset: assets/fonts/Poppins-Thin.ttf
        - asset: assets/fonts/Poppins-ThinItalic.ttf