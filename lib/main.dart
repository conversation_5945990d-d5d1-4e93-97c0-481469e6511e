import 'dart:io';
import 'package:ds_admin/framework/provider/local_storage/hive/hive_provider.dart';
import 'package:ds_admin/framework/provider/local_storage/local_const.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:easy_localization_loader/easy_localization_loader.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:ds_admin/framework/controller/dark_mode/theme_mode_controller.dart';
import 'package:ds_admin/framework/dependency_injection/inject.dart';
import 'package:ds_admin/ui/routing/delegate.dart';
import 'package:ds_admin/ui/routing/parser.dart';
import 'package:ds_admin/ui/routing/stack.dart';
import 'package:ds_admin/ui/utils/const/app_constants.dart';
import 'package:ds_admin/ui/utils/theme/theme.dart';
import 'package:ds_admin/ui/utils/theme/theme_style.dart';
import 'package:url_strategy/url_strategy.dart';

final snackbarKey = GlobalKey<ScaffoldMessengerState>();
final GlobalKey<NavigatorState> globalNavigatorKey = GlobalKey<NavigatorState>();


Future<void> main() async {
  print('version: 1.0.0+7');
  await HiveProvider.init();

  WidgetsFlutterBinding.ensureInitialized();
  await configureMainDependencies(environment: Env.prod);
  await EasyLocalization.ensureInitialized();
  await Hive.initFlutter();
  await Hive.openBox(LocalConst.appBox);

  /// Theme For Status Bar & Navigation Bar
  SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
    /// The color of top status bar.
    ///
    /// Only honored in Android version M and greater.
    statusBarColor: Colors.transparent,

    /// The brightness of the top status bar icons.
    ///
    /// Only honored in Android version M and greater.
    statusBarIconBrightness: Brightness.dark,

    /// The brightness of top status bar.
    ///
    /// Only honored in iOS.
    statusBarBrightness: Brightness.dark,

    /// The color of the system bottom navigation bar.
    ///
    /// Only honored in Android versions O and greater.
    systemNavigationBarColor: AppColors.black,

    /// The brightness of the system navigation bar icons.
    ///
    /// Only honored in Android versions O and greater.
    /// When set to [Brightness.light], the system navigation bar icons are light.
    /// When set to [Brightness.dark], the system navigation bar icons are dark.
    systemNavigationBarIconBrightness: Brightness.dark,
  ));

  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  setPathUrlStrategy();

  runApp(ProviderScope(
    child: EasyLocalization(
      supportedLocales: const <Locale>[
        Locale('gu'),
        Locale('en'),
        Locale('hi'),
      ],
      fallbackLocale: Locale('gu'),
      useOnlyLangCode: true,
      path: 'lang',
      assetLoader: JsonAssetLoader(),
      startLocale: Locale('gu'),
      child: const MyApp(),
    ),
  ));
}

class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)..badCertificateCallback = (X509Certificate cert, String host, int port) => true;
  }
}

class MyApp extends ConsumerStatefulWidget {
  /// Global NavigatorKey
  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
  const MyApp({super.key});

  @override
  ConsumerState<MyApp> createState() => _MyAppState();
}

class _MyAppState extends ConsumerState<MyApp> {

  /// dispose method
  @override
  void dispose() {
    Hive.box('userBox').compact();
    Hive.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    globalContext = context;
    var themeModeController = ref.watch(themeModeProvider);

    /// Theme For Status Bar & Navigation Bar
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
      /// The color of top status bar.
      ///
      /// Only honored in Android version M and greater.
      statusBarColor: Colors.transparent,

      /// The brightness of the top status bar icons.
      ///
      /// Only honored in Android version M and greater.
      statusBarIconBrightness: AppColors.isDarkMode ? Brightness.light : Brightness.dark,

      /// The brightness of top status bar.
      ///
      /// Only honored in iOS.
      statusBarBrightness: AppColors.isDarkMode ? Brightness.dark : Brightness.light,

      /// The color of the system bottom navigation bar.
      ///
      /// Only honored in Android versions O and greater.
      systemNavigationBarColor: AppColors.isDarkMode ? AppColors.white : AppColors.black,

      /// The brightness of the system navigation bar icons.
      ///
      /// Only honored in Android versions O and greater.
      /// When set to [Brightness.light], the system navigation bar icons are light.
      /// When set to [Brightness.dark], the system navigation bar icons are dark.
      systemNavigationBarIconBrightness: AppColors.isDarkMode ? Brightness.light : Brightness.dark,
    ));

    return MaterialApp.router(
      debugShowCheckedModeBanner: false,
      title: appName,
      themeMode: themeModeController.themeMode,
      theme: ThemeStyle.themeData(false, context),
      supportedLocales: context.supportedLocales,
      localizationsDelegates: context.localizationDelegates,
      locale: themeModeController.currentLocale,
      // home: const SplashScreen(),
      scaffoldMessengerKey: snackbarKey,
      backButtonDispatcher: RootBackButtonDispatcher(),
      routerDelegate: getIt<MainRouterDelegate>(param1: ref.read(navigationStackController)),
      routeInformationParser: getIt<MainRouterInformationParser>(param1: ref, param2: context),
    );
  }
}
