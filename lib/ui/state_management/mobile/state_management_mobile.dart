import 'dart:math';

import 'package:data_table_2/data_table_2.dart';
import 'package:ds_admin/framework/utils/extension/extension.dart';
import 'package:ds_admin/framework/utils/extension/string_extension.dart';
import 'package:ds_admin/ui/utils/widgets/change_language_widget.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../framework/controller/state_management/state_management_controller.dart';
import '../../../framework/repository/state_management/model/get_states_response.dart';
import '../../routing/navigation_stack_item.dart';
import '../../utils/theme/locale_keys.g.dart';
import '../../utils/theme/theme.dart';
import '../../utils/widgets/base_scaffold.dart';
import '../../utils/widgets/common_text.dart';
import '../../utils/widgets/dialog_progressbar.dart';

class StateManagementMobile extends ConsumerStatefulWidget {
  const StateManagementMobile({super.key});

  @override
  ConsumerState<StateManagementMobile> createState() => _StateManagementMobileState();
}

class _StateManagementMobileState extends ConsumerState<StateManagementMobile> {

  @override
  void initState() {
    super.initState();
    SchedulerBinding.instance.addPostFrameCallback((timeStamp) {
      final stateController = ref.read(stateManagementController);
      stateController.getStates(context: context);
    });

  }
  @override
  Widget build(BuildContext context) {
    var stateController = ref.watch(stateManagementController);
    return BaseScaffold(
      route: NavigationStackItem.stateManagement(),
      isMobile: true,
      appbar: AppBar(
        title: CommonText(title: LocaleKeys.keyStateManagement.localized, textStyle: TextStyles.medium.copyWith(color: AppColors.white),),
        centerTitle: true,
        backgroundColor: AppColors.primary,
        actions: [
          ChangeLanguageWidget(ref: ref,).paddingOnly(right: 20),
        ],
      ),
      body: Container(
        color: AppColors.white,
        height: MediaQuery.of(context).size.height-54,
        width: MediaQuery.of(context).size.width,
        child: Stack(
          children: [
            SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              LocaleKeys.keyStatesList.localized,
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            ElevatedButton.icon(
                              icon: Icon(Icons.add, color: AppColors.primary,),
                              label: Text(LocaleKeys.keyAddState.localized, style: TextStyles.regular.copyWith(color: AppColors.primary),),
                              style: ButtonStyle(
                                backgroundColor: WidgetStateProperty.all(AppColors.greyF6F6F6),
                              ),
                              onPressed: () => _showAddEditStateDialog(context),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        stateController.states.isNotEmpty ? _buildStatesTable() : _buildEmptyStateWidget(),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            DialogProgressBar(isLoading: stateController.isLoading),
          ],
        ),
      ),
    );
  }

  Widget _buildStatesTable() {
    var stateController = ref.watch(stateManagementController);
    return SizedBox(
      height: 500,
      width: max(MediaQuery.of(context).size.width, 810),
      child: DataTable2(
        columnSpacing: 12,
        dataRowHeight: 60,
        minWidth: 550,
        horizontalScrollController: ScrollController(),
        headingRowColor: WidgetStateProperty.all(Colors.grey.shade100),
        border: TableBorder.all(color: Colors.grey.shade300),
        columns: [
          DataColumn2(size: ColumnSize.S, /*fixedWidth: 100,*/ label: Text(LocaleKeys.keyID.localized)),
          DataColumn2(size: ColumnSize.L, /*fixedWidth: 140,*/ label: Text(LocaleKeys.keyName.localized)),
          DataColumn2(size: ColumnSize.S, /*fixedWidth: 100,*/ label: Text(LocaleKeys.keyCode.localized)),
          DataColumn2(size: ColumnSize.S, /*fixedWidth: 100,*/ label: Text(LocaleKeys.keyStatus.localized)),
          DataColumn2(size: ColumnSize.S, /*fixedWidth: 100,*/ label: Text(LocaleKeys.keyActions.localized)),
        ],
        rows: stateController.states.map((state) {
          return DataRow(
            cells: [
              DataCell(Text(state.stateId.toString())),
              DataCell(Text(state.stateName ?? '')),
              DataCell(Text(state.stateCode ?? '')),
              DataCell(
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: state.status == 'Active' ? Colors.green[100] : Colors.red[100],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    state.status ?? '',
                    style: TextStyle(
                      color: state.status == 'Active' ? Colors.green[800] : Colors.red[800],
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              DataCell(
                Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.edit, color: Colors.blue),
                      onPressed: () => _showAddEditStateDialog(context, state),
                    ),
                    IconButton(
                      icon: const Icon(Icons.delete, color: Colors.red),
                      onPressed: () => _showDeleteConfirmationDialog(context, state),
                    ),
                  ],
                ),
              ),
            ],
          );
        }).toList(),
      ),
    );
  }

  Widget _buildEmptyStateWidget() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 32),
      width: double.infinity,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.public_off,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            LocaleKeys.keyNoStatesFound.localized,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            LocaleKeys.keyAddFirstStateMsg.localized,
            style: TextStyle(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            icon: Icon(Icons.add_circle_outline, color: AppColors.primary,),
            label: Text(LocaleKeys.keyAddFirstState.localized, style: TextStyles.regular.copyWith(color: AppColors.primary),),
            style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                backgroundColor: AppColors.greyF6F6F6
            ),
            onPressed: () => _showAddEditStateDialog(context),
          ),
        ],
      ),
    );
  }

  void _showAddEditStateDialog(BuildContext context, [States? stateToEdit]) {
    final isEditing = stateToEdit != null;
    final formKey = GlobalKey<FormState>();
    var stateController = ref.read(stateManagementController);
    stateController.ctrlStateName.text = stateToEdit?.stateName ?? '';
    stateController.ctrlStateCode.text = stateToEdit?.stateCode ?? '';
    stateController.updateStateStatus(stateToEdit?.status ?? 'Active');

    showDialog(
      context: context,
      builder: (context) {
        var stateController = ref.watch(stateManagementController);
        return AlertDialog(
          title: Text(isEditing ? LocaleKeys.keyEditState.localized : LocaleKeys.keyAddNewState.localized),
          content: Form(
            key: formKey,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextFormField(
                    controller: stateController.ctrlStateName,
                    decoration: InputDecoration(
                      labelText: LocaleKeys.keyStateName.localized,
                      hintText: LocaleKeys.keyStateNameHint.localized,
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return LocaleKeys.keyErrStateName.localized;
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: stateController.ctrlStateCode,
                    decoration: InputDecoration(
                      labelText: LocaleKeys.keyStateCode.localized,
                      hintText: LocaleKeys.keyStateCodeHint.localized,
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return LocaleKeys.keyErrStateCode.localized;
                      }
                      if (value.length != 2) {
                        return LocaleKeys.keyErrStateCodeLength.localized;
                      }
                      return null;
                    },
                    textCapitalization: TextCapitalization.characters,
                    maxLength: 2,
                  ),
                  const SizedBox(height: 20),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      color: Colors.grey[100],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(LocaleKeys.keyStatus.localized,
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        const SizedBox(height: 12),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Consumer(
                              builder: (BuildContext context, WidgetRef ref, Widget? child) {
                                var controller = ref.watch(stateManagementController);
                                return Row(
                                  children: [
                                    Container(
                                      width: 16,
                                      height: 16,
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        color: controller.stateStatus == 'Active' ? Colors.green[600] : Colors.grey[400],
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      controller.stateStatus,
                                      style: TextStyle(
                                        fontWeight: FontWeight.w500,
                                        color: controller.stateStatus == 'Active' ? Colors.green[800] : Colors.grey[700],
                                      ),
                                    ),
                                  ],
                                );
                              },
                            ),
                            Consumer(
                              builder: (BuildContext context, WidgetRef ref, Widget? child) {
                                var controller = ref.watch(stateManagementController);
                                return Switch(
                                  value: controller.stateStatus == 'Active',
                                  activeColor: Colors.white,
                                  activeTrackColor: Colors.green[600],
                                  inactiveThumbColor: Colors.white,
                                  inactiveTrackColor: Colors.red[300],
                                  trackOutlineColor: WidgetStateProperty.resolveWith(
                                        (states) => Colors.transparent,
                                  ),
                                  thumbIcon: WidgetStateProperty.resolveWith<Icon?>(
                                        (Set<WidgetState> widgetStates) {
                                      if (widgetStates.contains(WidgetState.selected)) {
                                        return const Icon(Icons.check, color: Colors.green, size: 16);
                                      }
                                      return const Icon(Icons.close, color: Colors.red, size: 16);
                                    },
                                  ),
                                  onChanged: (bool value) {
                                    controller.updateStateStatus(value ? 'Active' : 'Inactive');
                                  },
                                );
                              },
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(LocaleKeys.keyCancel.localized, style: TextStyles.regular.copyWith(color: AppColors.red),),
            ),
            ElevatedButton(
              onPressed: () {
                if (formKey.currentState!.validate()) {
                  if (isEditing) {
                    _updateState(stateToEdit.stateId!);
                  } else {
                    _addNewState();
                  }
                  Navigator.of(context).pop();
                }
              },
              style: ButtonStyle(
                backgroundColor: WidgetStateProperty.all(AppColors.greyF6F6F6),
              ),
              child: Text(isEditing ? LocaleKeys.keyUpdate.localized : LocaleKeys.keyAdd.localized, style: TextStyles.regular.copyWith(color: AppColors.primary),),
            ),
          ],
        );
      },
    );
  }

  void _showDeleteConfirmationDialog(BuildContext context, States state) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(LocaleKeys.keyConfirmDelete.localized),
          content: Text('${LocaleKeys.keyDeleteStateMsg} "${state.stateName}"?'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(LocaleKeys.keyCancel.localized),
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              onPressed: () {
                _deleteState(state.stateId!);
                Navigator.of(context).pop();
              },
              child: Text(LocaleKeys.keyDelete.localized),
            ),
          ],
        );
      },
    );
  }

  void _addNewState() {
    ref.read(stateManagementController).addState();
  }

  void _updateState(int stateId) {
    ref.read(stateManagementController).updateState(stateId);
  }

  void _deleteState(int stateId) {
    ref.read(stateManagementController).deleteState(stateId);
  }
}
