import 'package:ds_admin/ui/state_management/web/state_management_web.dart';
import 'package:responsive_builder/responsive_builder.dart';

import '../utils/theme/theme.dart';
import 'mobile/state_management_mobile.dart';

class StateManagement extends StatefulWidget {
  const StateManagement({super.key});

  @override
  State<StateManagement> createState() => _StateManagementState();
}

class _StateManagementState extends State<StateManagement> {
  @override
  Widget build(BuildContext context) {
    return ScreenTypeLayout.builder(
      mobile: (BuildContext context) => const StateManagementMobile(),
      tablet: (BuildContext context) => const StateManagementWeb(),
      desktop: (BuildContext context) => const StateManagementWeb(),
    );
  }
}
