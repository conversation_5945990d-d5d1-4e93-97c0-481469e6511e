import 'package:ds_admin/ui/utils/theme/theme.dart';
import 'package:ds_admin/ui/utils/widgets/common_svg.dart';
import 'package:ds_admin/ui/utils/widgets/safe_on_tap_button.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';

class CommonButton extends StatelessWidget {
  final double? height;
  final double? width;
  final Color? backgroundColor;
  final Color? borderColor;
  final double? borderWidth;
  final BorderRadius? borderRadius;
  final String? leftImage;
  final double? leftImageHeight;
  final double? leftImageWidth;
  final double? leftImageHorizontalPadding;
  final String? rightImage;
  final bool? isrightImageSvg;
  final double? rightImageHeight;
  final double? rightImageWidth;
  final double? rightImageHorizontalPadding;
  final String? buttonText;
  final int? buttonMaxLine;
  final TextStyle? buttonTextStyle;
  final double? buttonHorizontalPadding;
  final GestureTapCallback? onTap;
  final TextAlign? buttonTextAlignment;
  final Color? buttonTextColor;
  final bool showLoader;
  final Color loadingColor;

  const CommonButton({
    super.key,
    this.height,
    this.width,
    this.backgroundColor,
    this.borderColor,
    this.borderWidth,
    this.borderRadius,
    this.leftImage,
    this.leftImageHeight,
    this.leftImageWidth,
    this.leftImageHorizontalPadding,
    this.rightImage,
    this.rightImageHeight,
    this.rightImageWidth,
    this.rightImageHorizontalPadding,
    this.buttonText,
    this.buttonMaxLine,
    this.buttonTextStyle,
    this.buttonHorizontalPadding,
    this.onTap,
    this.showLoader = false,
    this.buttonTextAlignment,
    this.buttonTextColor,
    this.isrightImageSvg = false,
    this.loadingColor = AppColors.white,
  });

  @override
  Widget build(BuildContext context) {
    return SafeOnTap(
      onSafeTap: () {
        if (showLoader == false) {
          onTap?.call();
        }
      },
      child: Container(
        height: height ?? 48.h,
        width: width ?? double.infinity,
        decoration: BoxDecoration(
            color: backgroundColor ?? AppColors.primary,
            borderRadius: borderRadius ?? BorderRadius.circular(10.r),
            border: Border.all(
                color: borderColor ?? AppColors.transparent,
                width: borderWidth ?? 0)),
        child: showLoader == true
            ? Center(
                child: LoadingAnimationWidget.waveDots(
                    color: loadingColor, size: 40.h))
            : Row(
                //mainAxisAlignment: MainAxisAlignment.spaceBetween,
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                // crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  if ((leftImage ?? '').isNotEmpty)
                    Padding(
                      padding: EdgeInsets.symmetric(
                          horizontal: leftImageHorizontalPadding ?? 12.w),
                      child: Image.asset(
                        leftImage!,
                        height: leftImageHeight,
                        width: leftImageWidth,
                      ),
                    ),
                  Padding(
                    padding: EdgeInsets.symmetric(
                        horizontal: buttonHorizontalPadding ?? 0),
                    child: Text(
                      buttonText ?? '',
                      textAlign: buttonTextAlignment ?? TextAlign.center,
                      maxLines: buttonMaxLine ?? 1,
                      style: buttonTextStyle ??
                          TextStyles.medium.copyWith(
                              fontSize: 14.sp,
                              color: buttonTextColor ?? AppColors.white),
                    ),
                  ),
                  if ((rightImage ?? '').isNotEmpty)
                    Padding(
                      padding: EdgeInsets.symmetric(
                          horizontal: rightImageHorizontalPadding ?? 12.w),
                      child: isrightImageSvg!
                          ? CommonSVG(
                              strIcon: rightImage!,
                              height: rightImageHeight,
                              width: rightImageWidth,
                            )
                          : Image.asset(
                              rightImage!,
                              height: rightImageHeight,
                              width: rightImageWidth,
                            ),
                    ),
                ],
              ),
      ),
    );
  }
}

/*
Widget Usage
CommonButton(
          buttonText: "Login",
          onTap: () {

          },
        )
* */
