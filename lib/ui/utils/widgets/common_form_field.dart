import 'package:ds_admin/ui/utils/const/app_constants.dart';
import 'package:flutter/services.dart';
import 'package:ds_admin/ui/utils/theme/theme.dart';

class CommonInputFormField extends StatelessWidget {
  final TextEditingController textEditingController;
  final String? Function(String?)? validator;

  final String? placeholderImage;
  final double? placeholderImageHeight;
  final double? placeholderImageWidth;
  final double? placeholderImageHorizontalPadding;
  final String? placeholderText;
  final TextStyle? placeholderTextStyle;
  final String? hintText;
  final TextStyle? hintTextStyle;
  final double? fieldWidth;
  final Color? backgroundColor;
  final Color? borderColor;
  final double? borderWidth;
  final BorderRadius? borderRadius;
  final TextStyle? fieldTextStyle;
  final int? maxLines;
  final int? maxLength;
  final FocusNode? focus;
  final List<TextInputFormatter>? textInputFormatter;
  final TextInputAction? textInputAction;
  final TextInputType? textInputType;
  final TextCapitalization? textCapitalization;
  final bool? isEnable;
  final Widget? prefixWidget;
  final Widget? suffixWidget;
  final InputDecoration? inputDecoration;
  final bool? obscureText;
  final double? bottomFieldMargin;
  final Function(dynamic text)? onChanged;
  final Function(dynamic text)? onSubmit;
  final Function()? onTap;
  final Widget? suffixLabel;
  final Color? cursorColor;
  final bool? enableInteractiveSelection;
  final bool? readOnly;
  final bool? rightPlaceHolder;
  final double? fieldHeight;
  final EdgeInsetsGeometry? contentPadding;
  final TextDirection? textDirectional;

  const CommonInputFormField(
      {super.key,
      required this.textEditingController,
      required this.validator,
      this.placeholderImage,
      this.onTap,
      this.placeholderImageHeight,
      this.placeholderImageWidth,
      this.placeholderImageHorizontalPadding,
      this.placeholderText,
      this.placeholderTextStyle,
      this.hintText,
      this.hintTextStyle,
      this.fieldWidth,
      this.backgroundColor,
      this.borderColor,
      this.borderWidth,
      this.borderRadius,
      this.fieldTextStyle,
      this.maxLines,
      this.maxLength,
      this.textInputFormatter,
      this.textInputAction,
      this.textInputType,
      this.textCapitalization,
      this.isEnable,
      this.prefixWidget,
      this.suffixWidget,
      this.inputDecoration,
      this.obscureText,
      this.bottomFieldMargin,
      this.onChanged,
      this.onSubmit,
      this.suffixLabel,
      this.cursorColor,
      this.enableInteractiveSelection,
      this.readOnly,
      this.rightPlaceHolder = false,
      this.fieldHeight,
      this.textDirectional,
      this.focus,
      this.contentPadding});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (((placeholderImage ?? '').isNotEmpty) ||
            ((placeholderText ?? '').isNotEmpty))
          Padding(
            padding: EdgeInsets.only(bottom: 8.h),
            child: Row(
              mainAxisAlignment: rightPlaceHolder!
                  ? MainAxisAlignment.spaceBetween
                  : MainAxisAlignment.start,
              children: [
                if ((placeholderImage ?? '').isNotEmpty)
                  Padding(
                    padding: EdgeInsets.only(
                        right: placeholderImageHorizontalPadding ?? 0.w),
                    child: Image.asset(
                      placeholderImage!,
                      height: placeholderImageHeight ?? 32,
                      width: placeholderImageWidth ?? 32,
                    ),
                  ),
                if ((placeholderText ?? '').isNotEmpty)
                  Text(
                    placeholderText!,
                    style: placeholderTextStyle ??
                        TextStyles.regular.copyWith(
                            fontSize: 14.sp,
                            color: AppColors.black010101,
                            fontFamily: TextStyles.secondaryFontFamily),
                  ),
                if (suffixLabel != null) suffixLabel!
              ],
            ),
          ),
        SizedBox(
          width: fieldWidth ?? double.infinity,
          height: fieldHeight,
          child: Padding(
            padding: EdgeInsets.only(bottom: bottomFieldMargin ?? 0),
            child: TextFormField(
              readOnly: readOnly ?? false,
              onTapOutside: (val) {
                hideKeyboard(context);
              },
              focusNode: focus,
              onTap: onTap,
              cursorColor: cursorColor ?? AppColors.primary,
              controller: textEditingController,
              style: fieldTextStyle ?? TextStyles.regular,
              textAlign: TextAlign.start,
              textAlignVertical: TextAlignVertical.center,
              maxLines: maxLines ?? 1,
              maxLength: maxLength ?? 1000,
              enableInteractiveSelection: enableInteractiveSelection ?? true,
              obscureText: obscureText ?? false,
              inputFormatters: textInputFormatter,
              onChanged: onChanged,
              textInputAction: textInputAction ?? TextInputAction.next,
              keyboardType: textInputType ?? TextInputType.text,
              textCapitalization: textCapitalization ?? TextCapitalization.none,
              decoration: InputDecoration(
                  errorStyle: TextStyles.regular.copyWith(fontSize: 12.sp,color: AppColors.red),
                errorMaxLines: 3,
                  enabled: isEnable ?? true,
                  counterText: '',
                  filled: true,
                  fillColor: backgroundColor ?? AppColors.transparent,
                  suffixIcon: suffixWidget != null
                      ? Padding(
                          padding: const EdgeInsets.all(2), child: suffixWidget)
                      : null,
                  prefixIcon: prefixWidget,
                  contentPadding: contentPadding ??
                      EdgeInsets.symmetric(vertical: 12.h, horizontal: 12.w),
                  disabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(
                      color: AppColors.black292929,
                      width: borderWidth ?? 1,
                      style: BorderStyle.solid,
                    ),
                    borderRadius: borderRadius ?? BorderRadius.circular(0.r),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(
                      color: borderColor ?? AppColors.black292929,
                      width: borderWidth ?? 1,
                      style: BorderStyle.solid,
                    ),
                    borderRadius: borderRadius ?? BorderRadius.circular(0.r),
                  ),
                  focusedErrorBorder: OutlineInputBorder(
                    borderSide: BorderSide(
                      color: AppColors.red,
                      width: borderWidth ?? 1,
                      style: BorderStyle.solid,
                    ),
                    borderRadius: borderRadius ?? BorderRadius.circular(0.r),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(
                      color: borderColor ?? AppColors.black292929,
                      width: borderWidth ?? 1,
                      style: BorderStyle.solid,
                    ),
                    borderRadius: borderRadius ?? BorderRadius.circular(0.r),
                  ),
                  errorBorder: OutlineInputBorder(
                    borderSide: BorderSide(
                      color: AppColors.red,
                      width: borderWidth ?? 1,
                      style: BorderStyle.solid,
                    ),
                    borderRadius: borderRadius ?? BorderRadius.circular(0.r),
                  ),
                  border: InputBorder.none,
                  hintText: hintText,
                  alignLabelWithHint: true,
                  hintStyle: hintTextStyle),
              onFieldSubmitted: onSubmit ?? (text) {
                textEditingController.text = text;
              },
              validator: validator,
              textDirection: textDirectional,
            ),
          ),
        )
      ],
    );
  }
}
/*
Widget Usage

CommonInputFormField(
  textEditingController: _mobileController,
  suffixWidget: Image.asset(Assets.imagesIcApple),
  validator: validateEmail,
  backgroundColor: AppColors.pinch,
  prefixWidget: Image.asset(Assets.imagesIcApple),
  placeholderImage: Assets.imagesIcApple,
  placeholderText: 'Mobile Number',
)
*/
