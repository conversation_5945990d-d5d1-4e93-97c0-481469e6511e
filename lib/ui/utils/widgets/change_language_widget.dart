import 'package:ds_admin/framework/controller/dark_mode/theme_mode_controller.dart';
import 'package:ds_admin/framework/utils/session.dart';
import 'package:ds_admin/ui/utils/widgets/common_text.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../theme/theme.dart';
import 'dart:html' as html;

class ChangeLanguageWidget extends StatelessWidget {
  const ChangeLanguageWidget({
    super.key,
    required this.ref,
  });

  final WidgetRef ref;

  @override
  Widget build(BuildContext context) {
    var controller = ref.watch(themeModeProvider);
    return PopupMenuButton(
      icon: Row(
        children: [
          Icon(Icons.translate, size: 16, color: AppColors.white,),
          CommonText(
            title: ': ${Session.getAppLanguage() == 'gu' ? 'Gujarati' : Session.getAppLanguage() == 'hi' ? 'Hindi' : 'English'}',
            textStyle: TextStyles.extraBold.copyWith(color: AppColors.white, fontSize: 16),
          ),
        ],
      ),
      padding: const EdgeInsets.all(0),
      offset: const Offset(0, 50),

      onSelected: (int pos) async {
        if (pos == 1) {
          controller.updateLocale('gu');
        }
        if (pos == 2){
          controller.updateLocale('hi');
        }
        if (pos == 3){
          controller.updateLocale('en');
        }
        html.window.location.reload();
      },
      itemBuilder: (context) {
        return [
          // Gujarati
          PopupMenuItem<int>(
            value: 1,
            height: 35.h,
            padding: EdgeInsets.symmetric(
              horizontal: 8.w,
            ),
            child: SizedBox(
                width: 130.w,
                child: CommonText(
                  title: "Gujarati",
                  textStyle: TextStyles.thin,
                  softWrap: true,
                )),
          ),
          // Hindi
          PopupMenuItem<int>(
            value: 2,
            height: 35.h,
            padding: EdgeInsets.symmetric(
              horizontal: 8.w,
            ),
            child: SizedBox(
                width: 130.w,
                child: CommonText(
                  title: "Hindi",
                  textStyle: TextStyles.thin,
                  softWrap: true,
                )),
          ),
          //English
          PopupMenuItem<int>(
            value: 3,
            height: 35.h,
            padding: EdgeInsets.symmetric(
              horizontal: 8.w,
            ),
            child: SizedBox(
                width: 130.w,
                child: CommonText(
                  title: "English",
                  textStyle: TextStyles.thin,
                  softWrap: true,
                )),
          ),
        ];
      },
    );
  }
}