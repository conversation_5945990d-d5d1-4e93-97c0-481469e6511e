import 'package:ds_admin/framework/utils/extension/extension.dart';
import 'package:ds_admin/framework/utils/extension/string_extension.dart';
import 'package:ds_admin/ui/utils/theme/locale_keys.g.dart';
import 'package:ds_admin/ui/utils/widgets/common_button.dart';
import 'package:ds_admin/ui/utils/theme/theme.dart';
import 'package:ds_admin/ui/utils/widgets/common_text.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../framework/utils/session.dart';

/// Confirmation dialog  message
showConfirmationDialog(BuildContext context, String title, String message, String btn1Name, String btn2Name, Function(bool isPositive) didTakeAction) {
  return showDialog(
    barrierDismissible: true,
    context: context,
    barrierColor: AppColors.bg1A2D3170,
    builder: (context) => Dialog(
      backgroundColor: AppColors.white,
      insetPadding: EdgeInsets.all(16.sp),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(ScreenUtil().setWidth(0.r))),
      child: Container(
        decoration: BoxDecoration(border: Border.all(color: AppColors.activeColor)),
        child: Stack(
          alignment: Alignment.center,
          children: <Widget>[
            Padding(
              padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 22.h, bottom: 15.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  message == '' ? SizedBox(height: 20.h) : const SizedBox(),
                  Text(title, textAlign: TextAlign.center, style: TextStyles.semiBold.copyWith(color: AppColors.black, fontSize: 20.sp)),
                  message != ''
                      ? SizedBox(
                          height: 18.h,
                        )
                      : const SizedBox(),
                  Text(message, textAlign: TextAlign.center, style: TextStyles.regular.copyWith(color: AppColors.black, fontSize: 20.sp)),
                  SizedBox(
                    height: 26.h,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CommonButton(
                          width: 139.w,
                          height: 49.h,
                          buttonText: btn1Name,
                          borderRadius: BorderRadius.circular(0.r),
                          borderWidth: 1.w,
                          onTap: () {
                            Navigator.pop(context);
                            Future.delayed(const Duration(milliseconds: 80), () {
                              didTakeAction(true);
                            });
                          },
                          borderColor: AppColors.activeColor,
                          backgroundColor: AppColors.white,
                          buttonTextColor: AppColors.black),
                      SizedBox(
                        width: 15.w,
                      ),
                      CommonButton(
                          buttonText: btn2Name,
                          width: 139.w,
                          height: 49.h,
                          borderWidth: 1.w,
                          borderRadius: BorderRadius.circular(0.r),
                          onTap: () {
                            Navigator.pop(context);
                            Future.delayed(const Duration(milliseconds: 80), () {
                              didTakeAction(false);
                            });
                          },
                          borderColor: AppColors.greyCFCFCF,
                          backgroundColor: AppColors.white,
                          buttonTextColor: AppColors.black),
                    ],
                  ),
                  SizedBox(height: 20.h),
                ],
              ),
            ),
          ],
        ),
      ),
    ),
  );
}

/// Message Dialog
showMessageDialog(BuildContext context, String message, Function()? didDismiss) {
  return showDialog(
      barrierDismissible: false,
      context: context,
      barrierColor: AppColors.bg1A2D3170,
      builder: (context) => Dialog(
            backgroundColor: AppColors.white,
            insetPadding: EdgeInsets.all(16.sp),
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(ScreenUtil().setWidth(5))),
            child: Stack(
              alignment: Alignment.center,
              children: <Widget>[
                SizedBox(
                  width: double.infinity,
                  // height: ScreenUtil().setHeight(220),
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 25.w, vertical: 30.h),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Flexible(
                          child: Text(
                            message,
                            textAlign: TextAlign.center,
                            style: TextStyle(
                                fontSize: 16.sp,
                                color: AppColors.black,
                                // fontWeight: TextStyles.medium,
                                fontFamily: TextStyles.fontFamily),
                          ),
                        ),
                        SizedBox(
                          height: 20.h,
                        ),
                        CommonButton(
                          width: 100.w,
                          buttonText: LocaleKeys.keyOk.localized,
                          backgroundColor: AppColors.greenB4D88B,
                          borderColor: AppColors.black,
                          buttonTextColor: AppColors.black010101,
                          onTap: () {
                            Navigator.pop(context);
                            if (didDismiss != null) {
                              Future.delayed(const Duration(milliseconds: 80), () {
                                didDismiss();
                              });
                            }
                          },
                          borderWidth: 1.0,
                          borderRadius: BorderRadius.zero,
                          buttonTextStyle: TextStyles.medium.copyWith(color: AppColors.black010101, fontSize: 16.sp),
                        ).paddingSymmetric(horizontal: 20.0, vertical: 11.0)
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ));
}

/// Logout Dialog
showLogoutDialog(BuildContext context) {
  return showDialog(
      barrierDismissible: false,
      context: context,
      barrierColor: AppColors.black.withValues(alpha: 0.3),
      builder: (context) => Consumer(builder: (context, ref, _) {
            return Dialog(
              backgroundColor: AppColors.white,
              surfaceTintColor: AppColors.white,
              insetPadding: EdgeInsets.all(16.sp),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(
                  ScreenUtil().setWidth(10.r),
                ),
              ),
              child: Stack(
                alignment: Alignment.center,
                children: <Widget>[
                  SizedBox(
                    width: 400,
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 25.w, vertical: 30.h),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Flexible(
                            child: CommonText(
                              title: LocaleKeys.keySessionExpired.localized,
                              textAlign: TextAlign.center,
                              maxLines: 5,
                              textStyle: TextStyles.medium.copyWith(color: AppColors.black, fontSize: 16.sp),
                            ),
                          ),
                          SizedBox(height: 24.h),
                          CommonButton(
                            buttonTextColor: AppColors.white,
                            backgroundColor: AppColors.primary,
                            borderColor: AppColors.primary,
                            width: 150.w,
                            buttonText: LocaleKeys.keyLogin.localized,
                            onTap: () {
                              Session.sessionLogout(ref);
                            },
                          )
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            );
          }));
}
