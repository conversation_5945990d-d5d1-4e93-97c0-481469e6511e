import 'package:ds_admin/framework/utils/extension/extension.dart';
import 'package:ds_admin/framework/utils/extension/string_extension.dart';
import 'package:ds_admin/ui/utils/theme/locale_keys.g.dart';
import 'package:ds_admin/ui/utils/widgets/change_language_widget.dart';
import 'package:ds_admin/ui/utils/widgets/common_text.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../theme/theme.dart';

class CommonWebHeader extends StatelessWidget {
  const CommonWebHeader({
    super.key,
    required this.ref,
  });

  final WidgetRef ref;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 64,
      color: AppColors.primary,
      child: Row(
        children: [
          CommonText(
            title: LocaleKeys.keyDabgarSamaj.localized,
            textStyle: TextStyles.extraBold.copyWith(color: AppColors.white, fontSize: 26),
          ).paddingSymmetric(horizontal: 20),
          Spacer(),
          CommonText(
            title: DateFormat('dd MMM yyyy').format(DateTime.now()),
            textStyle: TextStyles.extraBold.copyWith(color: AppColors.white, fontSize: 16),
          ).paddingSymmetric(horizontal: 10),
          ChangeLanguageWidget(ref: ref,).paddingOnly(right: 20),
        ],
      ),
    );
  }
}