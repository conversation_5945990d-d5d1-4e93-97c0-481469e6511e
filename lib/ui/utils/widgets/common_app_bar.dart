import 'package:ds_admin/framework/utils/extension/extension.dart';
import '../theme/theme.dart';

class CommonAppBar extends StatelessWidget {
  const CommonAppBar(
      {super.key,
      required this.title,
      required this.onBackPressed,
      this.bgColor,
      this.titleTxtColor,
      this.backBtnColor,
      this.actionWidgets,
      required this.isShowBack,
      required this.isCenterTitle,
      this.customTitleWidget});
  final String title;
  final Function() onBackPressed;
  final Color? bgColor;
  final Color? titleTxtColor;
  final Color? backBtnColor;
  final bool isShowBack;
  final bool isCenterTitle;
  final List<Widget>? actionWidgets;
  final Widget? customTitleWidget;

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: bgColor ?? AppColors.white,
      foregroundColor: Colors.transparent,
      shadowColor: Colors.transparent,
      centerTitle: isCenterTitle,
      leadingWidth: 30.w,
      leading: isShowBack
          ? IconButton(
              icon: Icon(
                Icons.arrow_back_outlined,
                color: backBtnColor ?? AppColors.black,
              ),
              onPressed: onBackPressed,
            )
          : const SizedBox(),
      title: customTitleWidget ??
          Text(
            title,
            style: TextStyles.medium.copyWith(fontSize : 18.sp, color: titleTxtColor ?? AppColors.black),
          ).paddingOnly(top: 2.h),
      actions: actionWidgets ?? [],
    );
  }
}
