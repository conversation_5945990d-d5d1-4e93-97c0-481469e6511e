import 'package:ds_admin/framework/controller/sidebar/sidebar_controller.dart';
import 'package:ds_admin/framework/utils/extension/extension.dart';
import 'package:ds_admin/ui/routing/navigation_stack_item.dart';
import 'package:ds_admin/ui/utils/widgets/admin_scaffold.dart';
import 'package:ds_admin/ui/utils/widgets/side_bar.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../routing/stack.dart';
import '../theme/theme.dart';

class BaseScaffold extends ConsumerWidget {
  const BaseScaffold({super.key, required this.body, this.appbar, required this.route, required this.isMobile});

  final Widget body;
  final AppBar? appbar;
  final NavigationStackItem route;
  final bool isMobile;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var controller = ref.watch(sidebarController);
    return AdminScaffold(
      backgroundColor: AppColors.primary,
      leadingIcon: Icon(Icons.menu, color: AppColors.white,),
      appBar: appbar,
      sideBar: SideBar(
        backgroundColor: AppColors.primary,
        tileBackgroundColor: AppColors.white,
        activeBackgroundColor: AppColors.ascent,
        borderColor: AppColors.transparent,
        iconColor: AppColors.grey9F9F9F,
        activeIconColor: AppColors.primary,
        textStyle: TextStyles.medium.copyWith(fontSize: 16),
        activeTextStyle: TextStyles.medium.copyWith(color: AppColors.primary, fontSize: 16),
        items: controller.sideBarItems,
        selectedRoute: route,
        onSelected: (item) {
          if (kDebugMode) {
            print('sideBar: onTap(): title = ${item.title}, route = ${item.route}');
          }
          if (item.route != null && item.route != route) {
            // Navigator.of(context).pushNamed(item.route!);
            if (kDebugMode) {
              print('stack items: ${ref.read(navigationStackController).items}');
            }
            if (ref.read(navigationStackController).items.length > 1) {
              for (int i = 0; i < ref.read(navigationStackController).items.length; i++) {
                if (ref.read(navigationStackController).items[i] == item.route!) {
                  ref.read(navigationStackController).popUntil(item.route!);
                  break;
                } else {
                  ref.read(navigationStackController).pushRemove(item.route!);
                }
              }
            } else {
              ref.read(navigationStackController).pushRemove(item.route!);
            }
          }

        },
        header: Container(
          height: 150,
          width: double.infinity,
          color: AppColors.primary,
          child: Image.asset(AppAssets.dsLogo, height: 130, width: 130).alignAtCenter(),
        ),
      ),
      body: SingleChildScrollView(
        child: body,
      ),
    );
  }
}
