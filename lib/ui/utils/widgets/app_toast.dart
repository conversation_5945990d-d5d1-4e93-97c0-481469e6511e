import 'package:ds_admin/framework/utils/extension/extension.dart';
import 'package:ds_admin/ui/utils/theme/theme.dart';

class AppToast {
  // static void showToast(String message,
  //     {ToastGravity? gravity = ToastGravity.TOP_RIGHT, String? backgroundColor = "#FB3E3E", Color? textColor = Colors.white, double? fontSize = 16.0}) {
  //   Fluttertoast.showToast(
  //     msg: message,
  //     toastLength: Toast.LENGTH_LONG,
  //     gravity: gravity,
  //     textColor: textColor,
  //     timeInSecForIosWeb: 5,
  //     fontSize: fontSize,
  //     webBgColor: backgroundColor,
  //   );
  // }

  static void showSnackBar(String message, ScaffoldMessengerState? state, Function() onDismissSnackBarTap,
      {Color? backgroundColor = Colors.white, Color? textColor = Colors.white, double? fontSize = 14.0, IconData? iconImage, Color? iconColor, Color? decorationColor}) {
    var snackBar = SnackBar(
      backgroundColor: backgroundColor,
      margin: EdgeInsets.zero,
      padding: EdgeInsets.zero,
      content: Container(
        decoration: BoxDecoration(color: decorationColor ?? AppColors.black.withValues(alpha: 0.75), borderRadius: BorderRadius.circular(10.r)),
        child: Row(
          children: [
            Icon(
              iconImage ?? Icons.check_circle,
              size: 26.h,
              color: iconColor ?? AppColors.green,
            ).paddingSymmetric(horizontal: 15.w),
            Expanded(
              child: Text(
                message,
                maxLines: 4,
                style: TextStyle(color: textColor, fontSize: fontSize),
                textAlign: TextAlign.start,
              ),
            ),
            InkWell(
              onTap: onDismissSnackBarTap,
              child: Icon(
                Icons.clear,
                size: 26.h,
                color: textColor ?? AppColors.white,
              ).paddingSymmetric(horizontal: 15.w),
            ),
          ],
        ).paddingSymmetric(vertical: 15.h),
      ).paddingSymmetric(horizontal: 15.w, vertical: 10.h),
      behavior: SnackBarBehavior.floating,

      // margin: const EdgeInsets.only(bottom: 100, left: 30, right: 30),
    );
    state?.showSnackBar(snackBar);
  }
}