// ignore_for_file: deprecated_member_use

// import 'package:flutter_img/flutter_img.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:ds_admin/ui/utils/theme/theme.dart';
import 'package:ds_admin/framework/provider/network/network.dart';

class CommonSVG extends StatelessWidget {
  final String strIcon;
  final ColorFilter? colorFilter;
  final Color? svgColor;
  final double? height;
  final double? width;
  final BoxFit? boxFit;
  final bool? isFromNetwork;

  const CommonSVG({
    super.key,
    required this.strIcon,
    this.svgColor,
    this.height,
    this.width,
    this.boxFit,
    this.colorFilter,
    this.isFromNetwork,
  });

  @override
  Widget build(BuildContext context) {
    return (isFromNetwork == true)
        ? (strIcon.isNotEmpty)
            ? SvgPicture.network(
                strIcon,
                colorFilter: colorFilter,
                color: svgColor,
                height: height,
                width: width,
                fit: boxFit ?? BoxFit.contain,
              )
            : const Offstage()
        : SvgPicture.asset(
            strIcon,
            colorFilter: colorFilter,
            color: svgColor,
            height: height,
            width: width,
            fit: boxFit ?? BoxFit.contain,
          );
  }
}
