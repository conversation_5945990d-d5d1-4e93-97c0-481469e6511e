import 'dart:io';
import 'package:flutter/material.dart';
// import 'package:easy_localization/easy_localization.dart';

const String appName = 'Dabgar <PERSON>aj';
const String userBoxName = 'userBox';

String googlePlaceApiKey = '';

bool isInternetConnectionOn = true;

bool getIsIOSPlatform() => Platform.isIOS;

bool getIsAppleSignInSupport() => (iosVersion >= 13);
int iosVersion = 11;

String getDeviceType() => getIsIOSPlatform() ? 'ios' : 'android';

int contactNumberLength = 10;
int accountNumberLength = 18;
int zipCodeLength = 6;
String countryCode = '+91';
String currency = '';
int pageCount = 20;
int nameLength = 30;
int userNameLength = 25;
double appBarHeight = 60;

BuildContext? globalContext;

/// Hide Keyboard
hideKeyboard(BuildContext context) {
  // FocusScope.of(context).unfocus();
  FocusManager.instance.primaryFocus?.unfocus();
}

///Show Log
showLog(String str) {
  debugPrint('-> $str');
}

// String generateFileName() {
//   return DateFormat('yyyy_MM_dd_HH_mm_ss_SSS').format(DateTime.now());
// }

/// Notification Channel Details
const String notificationChannelKey = 'basic_channel';
const String notificationChannelName = 'Basic Notification';
const String notificationChannelDescription = 'Notification channel for basic test';

const String apikeyGoogleIos = '';
const String apikeyGoogleAndroid = '';
const String apikeyGoogleAppIdIos = '';
const String apikeyGoogleAppIdAndroid = '';
const String apikeyGoogleMessagingSenderId = '';
const String apikeyGoogleProjectId = '';

/// Api Integration
const String developmentBaseUrl = 'https://api.dabgarsamaj.in/api/';
const String productionBaseUrl = 'https://api.dabgarsamaj.in/api/';
const String accept = 'Accept';
const String headerAccept = 'application/json';
const String contentType = 'Content-Type';
const String headerContentType = 'application/json';
const String acceptLanguage = 'Accept-Language';
const String production = 'production';
const String development = 'development';
const String authorization = 'Authorization';
const String bearer = 'Bearer';
const String responseType = 'responseType';
const String followRedirects = 'followRedirects';
const String connectTimeout = 'connectTimeout';
const String receiveTimeout = 'receiveTimeout';
const String headers = 'Headers';
const String extras = 'Extras';
const String queryParameters = 'Query Parameters';
const String get = 'GET';
const String body = 'Body';
const String formData = 'Form data';
const String dioError = 'DioError';
const String status = 'Status:';
const int initialTabNumber = 1;
const int dioLoggerMaxWidth = 90;
const String response = 'Response';
const String requestText = 'Request';

const Null nullValue = null;
const String nullString = 'null';
const String emptyString = '{}';
const String error = 'error';

const String stacktrace = 'stacktrace';

const String unknown = 'Unknown';
const String receivedInvalidStatusCode = 'Received invalid status code:';
const String socketException = 'SocketException';
const String formatException = 'FormatException';
const String isNotaSubtypeOf = 'is not a subtype of';
const String notImplemented = 'Not Implemented';
const String requestCancelled = 'Request Cancelled';
const String internalServerError = 'Internal Server Error';
const String serviceUnavailable = 'Service unavailable';
const String methodAllowed = 'Method Allowed';
const String badRequest = 'Bad request';
const String unauthorizedRequest = 'Unauthorized request';
const String unexpectedErrorOccurred = 'Unexpected error occurred';
const String connectionRequestTimeout = 'Connection request timeout';
const String noInternetConnection = 'No internet connection';
const String errorDueToaConflict = 'Error due to a conflict';
const String sendTimeoutInConnectionWithAPIServer = 'Send timeout in connection with API server';
const String unableToProcessTheData = 'Unable to process the data';
const String formatExceptionSomethingWentWrongWithData = 'FormatException something went wrong with data';
const String notAcceptable = 'Not acceptable';
const String badeCertificate = 'Bade Certificate';
const String connectionError = 'Connection error';

const String present = 'Present';
const String isEmailValidPattern = r'^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$';
const String isWebsiteInvalid = r'((https?:www\.)|(https?:\/\/)|(www\.))[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9]{1,6}(\/[-a-zA-Z0-9()@:%_\+.~#?&\/=]*)?';

const String savedLocalData = 'Saved Local Data';