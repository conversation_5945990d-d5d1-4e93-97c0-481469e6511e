

import 'package:ds_admin/framework/utils/extension/string_extension.dart';

bool isEmail(String input) => input.isEmailValid();

bool isPhone(String input) =>
    RegExp(r'^[\+]?[(]?[0-9]{3}[)]?[-\s\.]?[0-9]{3}[-\s\.]?[0-9]{4,6}$')
        .hasMatch(input);

String? validateText(String? value, String? errorMSG) {
  if (value == null || value.trim().length < 3) {
    return errorMSG;
  } else {
    return null;
  }
}

String? validateTag(List? value, String? errorMSG) {
  if (value!.isEmpty) {
    return errorMSG;
  } else {
    return null;
  }
}

String? validatePrice(String? value, String? errorMSG) {
  if (value == null || value.trim().isEmpty) {
    return errorMSG;
  } else {
    return null;
  }
}

String? validateMaximum(double? max, double? min, String? errorMSG) {
  if ((max! < min!) || (min == 0.0 && max == 0.0)) {
    return errorMSG;
  } else {
    return null;
  }
}

String? validateMinimum(double? max, double? min, String? errorMSG) {
  if ((min! > max!) || (min == 0.0 && max == 0.0)) {
    return errorMSG;
  } else {
    return null;
  }
}

String? validateName(String? value, String? errorMSG, String? errorMSG2) {
  if (value == null || value.isEmpty) {
    return errorMSG;
  } else if (value.trim().length < 3) {
    return errorMSG2;
  } else {
    return null;
  }
}

String? validateEmpty(String? value, String? errorMSG) {
  if (value == null || value.trim().isEmpty) {
    return errorMSG;
  } else {
    return null;
  }
}
