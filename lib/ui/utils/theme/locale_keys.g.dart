// DO NOT EDIT. This is code generated via package:easy_localization/generate.dart

// ignore_for_file: constant_identifier_names

abstract class  LocaleKeys {
  static const keyBack = 'keyBack';
  static const keyNo = 'keyNo';
  static const keyYes = 'keyYes';
  static const keyOk = 'keyOk';
  static const keyEdit = 'keyEdit';
  static const keyCancel = 'keyCancel';
  static const keyAdd = 'keyAdd';
  static const keyDabgarSamaj = 'keyDabgarSamaj';
  static const keySessionExpired = 'keySessionExpired';
  static const keyLogin = 'keyLogin';
  static const keyWelcomeTO = 'keyWelcomeTO';
  static const keyWelcome = 'keyWelcome';
  static const keyLoginToAdminPanel = 'keyLoginToAdminPanel';
  static const keyUsername = 'keyUsername';
  static const keyPassword = 'keyPassword';
  static const keyEnterUsername = 'keyEnterUsername';
  static const keyEnterPassword = 'keyEnterPassword';
  static const keyDashboard = 'keyDashboard';
  static const keyMemberManagement = 'keyMemberManagement';
  static const keyEventManagement = 'keyEventManagement';
  static const keyStateManagement = 'keyStateManagement';
  static const keyCityManagement = 'keyCityManagement';
  static const keyRules = 'keyRules';
  static const keyAbout = 'keyAbout';
  static const keyTotalPopulation = 'keyTotalPopulation';
  static const keyPopulation18PlusMale = 'keyPopulation18PlusMale';
  static const keyPopulation18PlusFemale = 'keyPopulation18PlusFemale';
  static const keyTotalCities = 'keyTotalCities';
  static const keyTotalStates = 'keyTotalStates';
  static const keyTotalMembers = 'keyTotalMembers';
  static const keyActiveMembers = 'keyActiveMembers';
  static const keyNaatbahar = 'keyNaatbahar';
  static const keyMembersList = 'keyMembersList';
  static const keyAddMember = 'keyAddMember';
  static const keySearchByName = 'keySearchByName';
  static const keyApplyFilter = 'keyApplyFilter';
  static const keyName = 'keyName';
  static const keyMobile = 'keyMobile';
  static const keyAge = 'keyAge';
  static const keyCity = 'keyCity';
  static const keyAddress = 'keyAddress';
  static const keyStatus = 'keyStatus';
  static const keyActions = 'keyActions';
  static const keyEditEvent = 'keyEditEvent';
  static const keyAddNewEvent = 'keyAddNewEvent';
  static const keyEventTitle = 'keyEventTitle';
  static const keyErrEventTitle = 'keyErrEventTitle';
  static const keyEventDate = 'keyEventDate';
  static const keyErrEventDate = 'keyErrEventDate';
  static const keyEventPhoto = 'keyEventPhoto';
  static const keyChooseImage = 'keyChooseImage';
  static const keyNewImageSelected = 'keyNewImageSelected';
  static const keyExistingImage = 'keyExistingImage';
  static const keyEventDescription = 'keyEventDescription';
  static const keyEnterEventDescription = 'keyEnterEventDescription';
  static const keyUpdateEvent = 'keyUpdateEvent';
  static const keyAddEvent = 'keyAddEvent';
  static const keyErrEventDescription = 'keyErrEventDescription';
  static const keyEventList = 'keyEventList';
  static const keySearchByTitle = 'keySearchByTitle';
  static const keyConfirmDelete = 'keyConfirmDelete';
  static const keyConfirmDeleteMsg = 'keyConfirmDeleteMsg';
  static const keyDelete = 'keyDelete';
  static const keyImageUnavailable = 'keyImageUnavailable';
  static const keyNoEventFound = 'keyNoEventFound';
  static const keyAddNewEventMsg = 'keyAddNewEventMsg';
  static const keyTryAdjustingSearch = 'keyTryAdjustingSearch';
  static const keyStatesList = 'keyStatesList';
  static const keyAddState = 'keyAddState';
  static const keyID = 'keyID';
  static const keyCode = 'keyCode';
  static const keyNoStatesFound = 'keyNoStatesFound';
  static const keyAddFirstStateMsg = 'keyAddFirstStateMsg';
  static const keyAddFirstState = 'keyAddFirstState';
  static const keyEditState = 'keyEditState';
  static const keyAddNewState = 'keyAddNewState';
  static const keyStateName = 'keyStateName';
  static const keyStateNameHint = 'keyStateNameHint';
  static const keyErrStateName = 'keyErrStateName';
  static const keyStateCode = 'keyStateCode';
  static const keyStateCodeHint = 'keyStateCodeHint';
  static const keyErrStateCode = 'keyErrStateCode';
  static const keyErrStateCodeLength = 'keyErrStateCodeLength';
  static const keyUpdate = 'keyUpdate';
  static const keyDeleteStateMsg = 'keyDeleteStateMsg';
  static const keyCityList = 'keyCityList';
  static const keyAddCity = 'keyAddCity';
  static const keyState = 'keyState';
  static const keyNoCityFound = 'keyNoCityFound';
  static const keyAddFirstCityMsg = 'keyAddFirstCityMsg';
  static const keyAddFirstCity = 'keyAddFirstCity';
  static const keyEditCity = 'keyEditCity';
  static const keyAddNewCity = 'keyAddNewCity';
  static const keyCityName = 'keyCityName';
  static const keyCityNameHint = 'keyCityNameHint';
  static const keyErrCityName = 'keyErrCityName';
  static const keyCityCode = 'keyCityCode';
  static const keyCityCodeHint = 'keyCityCodeHint';
  static const keyErrCityCode = 'keyErrCityCode';
  static const keyErrCityCodeLength = 'keyErrCityCodeLength';
  static const keySelectState = 'keySelectState';
  static const keyDeleteCityMsg = 'keyDeleteCityMsg';
  static const keyRulesTitle = 'keyRulesTitle';
  static const keyErrRulesTitle = 'keyErrRulesTitle';
  static const keyDsRules = 'keyDsRules';
  static const keyErrDsRules = 'keyErrDsRules';
  static const keyAboutTitle = 'keyAboutTitle';
  static const keyErrAboutTitle = 'keyErrAboutTitle';
  static const keyDsAbout = 'keyDsAbout';
  static const keyErrDsAbout = 'keyErrDsAbout';
  static const keyMinimumAge = 'keyMinimumAge';
  static const keyMaximumAge = 'keyMaximumAge';
  static const keyMartialStatus = 'keyMartialStatus';
  static const keySelectCity = 'keySelectCity';
  static const keyResetFilter = 'keyResetFilter';
  static const keyEditMember = 'keyEditMember';
  static const keyBasicInformation = 'keyBasicInformation';
  static const keyMemberName = 'keyMemberName';
  static const keyNickName = 'keyNickName';
  static const keyContactInformation = 'keyContactInformation';
  static const keyMobileNumber = 'keyMobileNumber';
  static const keyAlternateNumber = 'keyAlternateNumber';
  static const keyLocationInformation = 'keyLocationInformation';
  static const keyPincode = 'keyPincode';
  static const keyPanchCity = 'keyPanchCity';
  static const keyFamilyInformation = 'keyFamilyInformation';
  static const keyFather = 'keyFather';
  static const keyMother = 'keyMother';
  static const keySpouse = 'keySpouse';
  static const keyPersonalInformation = 'keyPersonalInformation';
  static const keyEmail = 'keyEmail';
  static const keyDateofBirth = 'keyDateofBirth';
  static const keyMaritalStatus = 'keyMaritalStatus';
  static const keyEducationalInformation = 'keyEducationalInformation';
  static const keyGraduation = 'keyGraduation';
  static const keySchoolCollegeName = 'keySchoolCollegeName';
  static const keyProfessionalInfo = 'keyProfessionalInfo';
  static const keyOccupation = 'keyOccupation';
  static const keyCompanyFirm = 'keyCompanyFirm';
  static const keyExperience = 'keyExperience';
  static const keyOtherBusiness = 'keyOtherBusiness';
  static const keyHSC = 'keyHSC';
  static const keySSC = 'keySSC';
  static const keyIsPhysicallyChallenged = 'keyIsPhysicallyChallenged';
  static const keyOtherInformation = 'keyOtherInformation';
  static const keyPhysicallyChallengedDetails = 'keyPhysicallyChallengedDetails';
  static const keyGender = 'keyGender';
  static const keySearchChildrentoadd = 'keySearchChildrentoadd';
  static const keyChildren = 'keyChildren';
  static const keySave = 'keySave';

}
