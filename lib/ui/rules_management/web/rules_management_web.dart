import 'package:ds_admin/framework/controller/rules_management/rules_management_controller.dart';
import 'package:ds_admin/framework/utils/extension/extension.dart';
import 'package:ds_admin/framework/utils/extension/string_extension.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:html_editor_enhanced/html_editor.dart';

import '../../routing/navigation_stack_item.dart';
import '../../utils/theme/locale_keys.g.dart';
import '../../utils/theme/theme.dart';
import '../../utils/widgets/base_scaffold.dart';
import '../../utils/widgets/common_web_header.dart';
import '../../utils/widgets/dialog_progressbar.dart';

class RulesManagementWeb extends ConsumerStatefulWidget {
  const RulesManagementWeb({super.key});

  @override
  ConsumerState<RulesManagementWeb> createState() => _RulesManagementWebState();
}

class _RulesManagementWebState extends ConsumerState<RulesManagementWeb> {

  @override
  void initState() {
    super.initState();
    SchedulerBinding.instance.addPostFrameCallback((timeStamp) {
      final rulesController = ref.read(rulesManagementController);
      rulesController.getRulesApiCall(context: context);
    });
  }

  @override
  Widget build(BuildContext context) {
    var rulesController = ref.watch(rulesManagementController);
    return BaseScaffold(
      route: NavigationStackItem.rulesManagement(),
      isMobile: false,
      body: Stack(
        children: [
          Container(
            color: AppColors.white,
            height: MediaQuery.of(context).size.height,
            width: MediaQuery.of(context).size.width,
            child: Column(
              children: [
                CommonWebHeader(ref: ref),
                Expanded(
                  child: Card(
                    child: SingleChildScrollView(
                      child: Form(
                        key: rulesController.formKey,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const SizedBox(height: 16),
                            TextFormField(
                              controller: rulesController.ctrlRuleTitle,
                              decoration: InputDecoration(
                                labelText: LocaleKeys.keyRulesTitle.localized,
                              ),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return LocaleKeys.keyErrRulesTitle.localized;
                                }
                                return null;
                              },
                            ),
                            const SizedBox(height: 16),
                            Text(LocaleKeys.keyDsRules.localized,
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const SizedBox(height: 8),
                            SizedBox(
                              height: 350,
                              child: HtmlEditor(
                                controller: rulesController.htmlEditorController,
                                htmlEditorOptions: HtmlEditorOptions(
                                  hint: LocaleKeys.keyErrDsRules.localized,
                                  initialText: rulesController.extractTextFromHtml(rulesController.rulesResponse.data?.description ?? ''),
                                ),
                                htmlToolbarOptions: const HtmlToolbarOptions(
                                  defaultToolbarButtons: [
                                    ColorButtons(),
                                    ParagraphButtons(lineHeight: false, caseConverter: false),
                                    InsertButtons(
                                      link: true,
                                    ),
                                    OtherButtons(),
                                  ],
                                  toolbarType: ToolbarType.nativeGrid,
                                ),
                              ),
                            ),
                            const SizedBox(height: 16),
                            ElevatedButton(
                              onPressed: (){
                                rulesController.updateRules();
                              },
                              style: ButtonStyle(
                                fixedSize: WidgetStateProperty.all(Size(200, 35)),
                                backgroundColor: WidgetStateProperty.all(AppColors.greyF6F6F6),
                              ),
                              child: Text(
                                LocaleKeys.keyUpdate.localized,
                                style: TextStyle(color: AppColors.primary),
                              ),
                            ).alignAtCenter(),
                          ],
                        ),
                      ).paddingSymmetric(horizontal: 16, vertical: 16),
                    ),
                  ).paddingSymmetric(horizontal: 20, vertical: 20),
                ),
              ],
            ),
          ),
          DialogProgressBar(isLoading: rulesController.isLoading),
        ],
      ),
    );
  }
}
