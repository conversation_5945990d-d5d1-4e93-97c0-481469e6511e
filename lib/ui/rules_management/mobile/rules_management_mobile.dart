import 'package:ds_admin/framework/utils/extension/extension.dart';
import 'package:ds_admin/framework/utils/extension/string_extension.dart';
import 'package:ds_admin/ui/routing/navigation_stack_item.dart';
import 'package:ds_admin/ui/utils/widgets/base_scaffold.dart';
import 'package:ds_admin/ui/utils/widgets/change_language_widget.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:html_editor_enhanced/html_editor.dart';

import '../../../framework/controller/rules_management/rules_management_controller.dart';
import '../../../framework/controller/sidebar/sidebar_controller.dart';
import '../../utils/theme/locale_keys.g.dart';
import '../../utils/theme/theme.dart';
import '../../utils/widgets/common_text.dart';
import '../../utils/widgets/dialog_progressbar.dart';

class RulesManagementMobile extends ConsumerStatefulWidget {
  const RulesManagementMobile({super.key});

  @override
  ConsumerState<RulesManagementMobile> createState() => _RulesManagementMobileState();
}

class _RulesManagementMobileState extends ConsumerState<RulesManagementMobile> {

  @override
  void initState() {
    super.initState();
    SchedulerBinding.instance.addPostFrameCallback((timeStamp) {
      final rulesController = ref.read(rulesManagementController);
      rulesController.getRulesApiCall(context: context);
      ref.read(sidebarController).updateDrawerStatus(0);
    });
  }

  @override
  Widget build(BuildContext context) {
    var rulesController = ref.watch(rulesManagementController);
    return BaseScaffold(
      route: NavigationStackItem.rulesManagement(),
      isMobile: true,
      appbar: AppBar(
        title: CommonText(title: LocaleKeys.keyRules.localized, textStyle: TextStyles.medium.copyWith(color: AppColors.white),),
        centerTitle: true,
        backgroundColor: AppColors.primary,
        actions: [
          ChangeLanguageWidget(ref: ref,).paddingOnly(right: 20),
        ],
      ),
      body: Stack(
        children: [
          Container(
            color: AppColors.white,
            height: MediaQuery.of(context).size.height,
            width: MediaQuery.of(context).size.width,
            child: SingleChildScrollView(
              child: Form(
                key: rulesController.formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: rulesController.ctrlRuleTitle,
                      decoration: InputDecoration(
                        labelText: LocaleKeys.keyRulesTitle.localized,
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return LocaleKeys.keyErrRulesTitle.localized;
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),
                    Text(LocaleKeys.keyDsRules.localized,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Consumer(
                      builder: (BuildContext context, WidgetRef ref, Widget? child) {
                        var controller = ref.watch(sidebarController);
                        return SizedBox(
                          height: 550.h,
                          child: controller.isDrawerOpen == 1 ? SizedBox() : HtmlEditor(
                            controller: rulesController.htmlEditorController,
                            htmlEditorOptions: HtmlEditorOptions(
                              hint: LocaleKeys.keyErrDsRules.localized,
                              initialText: rulesController.extractTextFromHtml(rulesController.rulesResponse.data?.description ?? ''),
                            ),
                            htmlToolbarOptions: const HtmlToolbarOptions(
                              defaultToolbarButtons: [
                                ColorButtons(),
                                ParagraphButtons(lineHeight: false, caseConverter: false),
                                InsertButtons(
                                  link: true,
                                ),
                                OtherButtons(),
                              ],
                              toolbarType: ToolbarType.nativeGrid,
                            ),
                          ),
                        );
                      },
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: (){
                        rulesController.updateRules();
                      },
                      style: ButtonStyle(
                        fixedSize: WidgetStateProperty.all(Size(200, 35)),
                        backgroundColor: WidgetStateProperty.all(AppColors.greyF6F6F6),
                      ),
                      child: Text(
                        LocaleKeys.keyUpdate.localized,
                        style: TextStyle(color: AppColors.primary),
                      ),
                    ).alignAtCenter(),
                  ],
                ),
              ).paddingSymmetric(horizontal: 16, vertical: 16),
            ),
          ),
          DialogProgressBar(isLoading: rulesController.isLoading),
        ],
      ),
    );
  }
}
