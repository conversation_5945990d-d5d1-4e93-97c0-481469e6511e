import 'package:responsive_builder/responsive_builder.dart';

import '../utils/theme/theme.dart';
import 'mobile/rules_management_mobile.dart';
import 'web/rules_management_web.dart';

class RulesManagement extends StatefulWidget {
  const RulesManagement({super.key});

  @override
  State<RulesManagement> createState() => _RulesManagementState();
}

class _RulesManagementState extends State<RulesManagement> {
  @override
  Widget build(BuildContext context) {
    return ScreenTypeLayout.builder(
      mobile: (BuildContext context) => const RulesManagementMobile(),
      tablet: (BuildContext context) => const RulesManagementWeb(),
      desktop: (BuildContext context) => const RulesManagementWeb(),
    );
  }
}
