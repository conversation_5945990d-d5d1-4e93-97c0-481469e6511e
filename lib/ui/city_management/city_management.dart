import 'package:responsive_builder/responsive_builder.dart';

import '../utils/theme/theme.dart';
import 'mobile/city_management_mobile.dart';
import 'web/city_management_web.dart';

class CityManagement extends StatefulWidget {
  const CityManagement({super.key});

  @override
  State<CityManagement> createState() => _CityManagementState();
}

class _CityManagementState extends State<CityManagement> {
  @override
  Widget build(BuildContext context) {
    return ScreenTypeLayout.builder(
      mobile: (BuildContext context) => const CityManagementMobile(),
      tablet: (BuildContext context) => const CityManagementWeb(),
      desktop: (BuildContext context) => const CityManagementWeb(),
    );
  }
}
