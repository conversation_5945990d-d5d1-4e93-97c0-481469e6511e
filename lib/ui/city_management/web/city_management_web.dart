import 'dart:math';

import 'package:data_table_2/data_table_2.dart';
import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:ds_admin/framework/controller/city_management/city_management_controller.dart';
import 'package:ds_admin/framework/repository/city_management/model/get_cities_response.dart';
import 'package:ds_admin/framework/utils/extension/string_extension.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../routing/navigation_stack_item.dart';
import '../../utils/theme/locale_keys.g.dart';
import '../../utils/theme/theme.dart';
import '../../utils/widgets/base_scaffold.dart';
import '../../utils/widgets/common_web_header.dart';
import '../../utils/widgets/dialog_progressbar.dart';

class CityManagementWeb extends ConsumerStatefulWidget {
  const CityManagementWeb({super.key});

  @override
  ConsumerState<CityManagementWeb> createState() => _CityManagementWebState();
}

class _CityManagementWebState extends ConsumerState<CityManagementWeb> {

  @override
  void initState() {
    super.initState();
    SchedulerBinding.instance.addPostFrameCallback((timeStamp) {
      final cityController = ref.read(cityManagementController);
      cityController.getCities(context: context);
      cityController.getStates();
    });
  }

  @override
  Widget build(BuildContext context) {
    final cityController = ref.watch(cityManagementController);
    return BaseScaffold(
      route: NavigationStackItem.cityManagement(),
      isMobile: false,
      body: Stack(
        children: [
          Container(
            color: AppColors.white,
            height: MediaQuery.of(context).size.height,
            width: MediaQuery.of(context).size.width,
            child: Column(
              children: [
                CommonWebHeader(ref: ref),
                Expanded(
                  child: ListView(
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Card(
                          child: SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: SizedBox(
                              width: max(MediaQuery.of(context).size.width - 280, 800),
                              child: Padding(
                                padding: const EdgeInsets.all(16.0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(LocaleKeys.keyCityList.localized,
                                          style: TextStyle(
                                            fontSize: 18,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                        ElevatedButton.icon(
                                          icon: Icon(Icons.add, color: AppColors.primary,),
                                          label: Text(LocaleKeys.keyAddCity.localized, style: TextStyles.regular.copyWith(color: AppColors.primary),),
                                          style: ButtonStyle(
                                            backgroundColor: WidgetStateProperty.all(AppColors.greyF6F6F6),
                                          ),
                                          onPressed: () => _showAddEditCityDialog(context),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 16),
                                    _buildSearchAndFilters(),
                                    const SizedBox(height: 16),
                                    cityController.cities.isNotEmpty ? _buildCitiesTable() : _buildEmptyCityWidget(),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                )
              ],
            ),
          ),
          DialogProgressBar(isLoading: cityController.isLoading),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilters() {
    final cityController = ref.watch(cityManagementController);
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Row(
              children: [
                // Search Field
                Expanded(
                  flex: 2,
                  child: TextFormField(
                    controller: cityController.searchController,
                    decoration: InputDecoration(
                      labelText: 'Search Cities',
                      hintText: 'Search by city name, code, or state...',
                      prefixIcon: const Icon(Icons.search),
                      suffixIcon: cityController.searchQuery.isNotEmpty
                          ? IconButton(
                              icon: const Icon(Icons.clear),
                              onPressed: () {
                                cityController.searchController.clear();
                                cityController.updateSearchQuery('');
                              },
                            )
                          : null,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                    ),
                    onChanged: (value) {
                      cityController.updateSearchQuery(value);
                    },
                  ),
                ),
                const SizedBox(width: 16),

                // Sort Dropdown
                Expanded(
                  flex: 1,
                  child: DropdownButtonFormField<String>(
                    value: cityController.sortOrder,
                    decoration: InputDecoration(
                      labelText: 'Sort Order',
                      prefixIcon: const Icon(Icons.sort),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                    ),
                    items: const [
                      DropdownMenuItem(
                        value: 'asc',
                        child: Row(
                          children: [
                            Icon(Icons.arrow_upward, size: 16),
                            SizedBox(width: 8),
                            Text('A-Z'),
                          ],
                        ),
                      ),
                      DropdownMenuItem(
                        value: 'desc',
                        child: Row(
                          children: [
                            Icon(Icons.arrow_downward, size: 16),
                            SizedBox(width: 8),
                            Text('Z-A'),
                          ],
                        ),
                      ),
                    ],
                    onChanged: (value) {
                      if (value != null) {
                        cityController.updateSortOrder(value);
                      }
                    },
                  ),
                ),
                const SizedBox(width: 16),

                // Status Filter Dropdown
                Expanded(
                  flex: 1,
                  child: DropdownButtonFormField<String>(
                    value: cityController.statusFilter,
                    decoration: InputDecoration(
                      labelText: 'Filter by Status',
                      prefixIcon: const Icon(Icons.filter_list),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                    ),
                    items: cityController.getAvailableStatuses().map((status) {
                      return DropdownMenuItem(
                        value: status,
                        child: Row(
                          children: [
                            Container(
                              width: 12,
                              height: 12,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: status == 'All'
                                    ? Colors.grey[400]
                                    : status == 'Active'
                                        ? Colors.green[600]
                                        : Colors.red[600],
                              ),
                            ),
                            const SizedBox(width: 8),
                            Text(status),
                          ],
                        ),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        cityController.updateStatusFilter(value);
                      }
                    },
                  ),
                ),
                const SizedBox(width: 16),

                // Clear Filters Button
                ElevatedButton.icon(
                  icon: const Icon(Icons.clear_all),
                  label: const Text('Clear'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey[100],
                    foregroundColor: Colors.grey[700],
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                  onPressed: () {
                    cityController.clearAllFilters();
                  },
                ),
              ],
            ),
            const SizedBox(height: 12),

            // Results Summary
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Showing ${cityController.cities.length} of ${cityController.originalCitiesCount} cities',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                ),
                if (cityController.searchQuery.isNotEmpty ||
                    cityController.statusFilter != 'All' ||
                    cityController.sortOrder != 'asc')
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      'Filters Applied',
                      style: TextStyle(
                        color: AppColors.primary,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCitiesTable() {
    var cityController = ref.watch(cityManagementController);
    return SizedBox(
      height: 500,
      child: DataTable2(
        columnSpacing: 12,
        dataRowHeight: 60,
        headingRowColor: WidgetStateProperty.all(Colors.grey.shade100),
        border: TableBorder.all(color: Colors.grey.shade300),
        columns: [
          DataColumn2(size: ColumnSize.S, label: Text(LocaleKeys.keyID.localized)),
          DataColumn2(size: ColumnSize.L, label: Text(LocaleKeys.keyCity.localized)),
          DataColumn2(size: ColumnSize.L, label: Text(LocaleKeys.keyState.localized)),
          DataColumn2(size: ColumnSize.S, label: Text(LocaleKeys.keyCode.localized)),
          DataColumn2(size: ColumnSize.S, label: Text(LocaleKeys.keyStatus.localized)),
          DataColumn2(size: ColumnSize.S, label: Text(LocaleKeys.keyActions.localized)),
        ],
        rows: cityController.cities.map((city) {
          return DataRow(
            cells: [
              DataCell(Text(city.cityId.toString())),
              DataCell(Text(city.cityName ?? '')),
              DataCell(Text(city.stateName ?? '')),
              DataCell(Text(city.cityCode ?? '')),
              DataCell(
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: city.status == 'Active' ? Colors.green[100] : Colors.red[100],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    city.status ?? '',
                    style: TextStyle(
                      color: city.status == 'Active' ? Colors.green[800] : Colors.red[800],
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              DataCell(
                Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.edit, color: Colors.blue),
                      onPressed: () => _showAddEditCityDialog(context, city),
                    ),
                    IconButton(
                      icon: const Icon(Icons.delete, color: Colors.red),
                      onPressed: () => _showDeleteConfirmationDialog(context, city),
                    ),
                  ],
                ),
              ),
            ],
          );
        }).toList(),
      ),
    );
  }

  Widget _buildEmptyCityWidget() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 32),
      width: double.infinity,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.home_work,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            LocaleKeys.keyNoCityFound.localized,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            LocaleKeys.keyAddFirstCityMsg.localized,
            style: TextStyle(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            icon: Icon(Icons.add_circle_outline, color: AppColors.primary,),
            label: Text(LocaleKeys.keyAddFirstCity.localized, style: TextStyles.regular.copyWith(color: AppColors.primary),),
            style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                backgroundColor: AppColors.greyF6F6F6
            ),
            onPressed: () => _showAddEditCityDialog(context),
          ),
        ],
      ),
    );
  }

  void _showAddEditCityDialog(BuildContext context, [City? cityToEdit]) {
    final isEditing = cityToEdit != null;
    final formKey = GlobalKey<FormState>();
    var cityController = ref.read(cityManagementController);
    cityController.ctrlCityName.text = cityToEdit?.cityName ?? '';
    cityController.ctrlCityCode.text = cityToEdit?.cityCode ?? '';
    cityController.updateCityStatus(cityToEdit?.status ?? 'Active');
    cityController.updateSelectedState(cityToEdit?.stateId ?? cityController.states.first.stateId!);

    showDialog(
      context: context,
      builder: (context) {
        var cityController = ref.watch(cityManagementController);
        return AlertDialog(
          title: Text(isEditing ? LocaleKeys.keyEditCity.localized : LocaleKeys.keyAddNewCity.localized),
          content: Form(
            key: formKey,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextFormField(
                    controller: cityController.ctrlCityName,
                    decoration: InputDecoration(
                      labelText: LocaleKeys.keyCityName.localized,
                      hintText: LocaleKeys.keyCityNameHint.localized,
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return LocaleKeys.keyErrCityName.localized;
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: cityController.ctrlCityCode,
                    decoration: InputDecoration(
                      labelText: LocaleKeys.keyCityCode.localized,
                      hintText: LocaleKeys.keyCityCodeHint.localized,
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return LocaleKeys.keyErrCityCode;
                      }
                      if (value.length < 2) {
                        return LocaleKeys.keyErrCityCodeLength.localized;
                      }
                      return null;
                    },
                    textCapitalization: TextCapitalization.characters,
                    maxLength: 3,
                  ),
                  const SizedBox(height: 20),

                  // State dropdown
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(LocaleKeys.keySelectState.localized,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.black54,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Consumer(
                        builder: (BuildContext context, WidgetRef ref, Widget? child) {
                          var controller = ref.watch(cityManagementController);
                          return DropdownButton2<int>(
                            isExpanded: false,
                            underline: Container(),
                            hint: Text(LocaleKeys.keySelectState.localized),
                            value: controller.ctrlStateId,
                            items: cityController.states.map((state) {
                              return DropdownMenuItem<int>(
                                value: state.stateId,
                                child: Row(
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 8,
                                        vertical: 2,
                                      ),
                                      decoration: BoxDecoration(
                                        color: AppColors.greyF6F6F6,
                                        borderRadius: BorderRadius.circular(4),
                                      ),
                                      child: Text(
                                        state.stateCode ?? '',
                                        style: TextStyle(
                                          color: Colors.blue[800],
                                          fontWeight: FontWeight.bold,
                                          fontSize: 12,
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Text(state.stateName ?? ''),
                                  ],
                                ),
                              );
                            }).toList(),
                            onChanged: (int? value) {
                              if (value != null) {
                                controller.updateSelectedState(value);
                              }
                            },
                            buttonStyleData: ButtonStyleData(
                              padding: const EdgeInsets.symmetric(horizontal: 16),
                              height: 50,
                              width: double.infinity,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(4),
                                border: Border.all(color: Colors.grey.shade300),
                              ),
                            ),
                            dropdownStyleData: DropdownStyleData(
                              maxHeight: 200,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(4),
                                color: AppColors.white
                              ),

                            ),
                            menuItemStyleData: MenuItemStyleData(
                              height: 40,
                              overlayColor: WidgetStateProperty.all(AppColors.white),
                            ),
                          );
                        },
                      ),
                    ],
                  ),

                  const SizedBox(height: 20),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      color: Colors.grey[100],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(LocaleKeys.keyStatus.localized,
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        const SizedBox(height: 12),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Consumer(
                              builder: (BuildContext context, WidgetRef ref, Widget? child) {
                                var controller = ref.watch(cityManagementController);
                                return Row(
                                  children: [
                                    Container(
                                      width: 16,
                                      height: 16,
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        color: controller.cityStatus == 'Active' ? Colors.green[600] : Colors.grey[400],
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      controller.cityStatus,
                                      style: TextStyle(
                                        fontWeight: FontWeight.w500,
                                        color: controller.cityStatus == 'Active' ? Colors.green[800] : Colors.grey[700],
                                      ),
                                    ),
                                  ],
                                );
                              },
                            ),
                            Consumer(
                              builder: (BuildContext context, WidgetRef ref, Widget? child) {
                                var controller = ref.watch(cityManagementController);
                                return Switch(
                                  value: controller.cityStatus == 'Active',
                                  activeColor: Colors.white,
                                  activeTrackColor: Colors.green[600],
                                  inactiveThumbColor: Colors.white,
                                  inactiveTrackColor: Colors.red[300],
                                  trackOutlineColor: WidgetStateProperty.resolveWith(
                                        (states) => Colors.transparent,
                                  ),
                                  thumbIcon: WidgetStateProperty.resolveWith<Icon?>(
                                        (Set<WidgetState> widgetStates) {
                                      if (widgetStates.contains(WidgetState.selected)) {
                                        return const Icon(Icons.check, color: Colors.green, size: 16);
                                      }
                                      return const Icon(Icons.close, color: Colors.red, size: 16);
                                    },
                                  ),
                                  onChanged: (bool value) {
                                    controller.updateCityStatus(value ? 'Active' : 'Inactive');
                                  },
                                );
                              },
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(LocaleKeys.keyCancel.localized, style: TextStyles.regular.copyWith(color: AppColors.red),),
            ),
            ElevatedButton(
              onPressed: () {
                if (formKey.currentState!.validate()) {
                  if (isEditing) {
                    _updateCity(cityToEdit.cityId!);
                  } else {
                    _addNewCity();
                  }
                  Navigator.of(context).pop();
                }
              },
              style: ButtonStyle(
                backgroundColor: WidgetStateProperty.all(AppColors.greyF6F6F6),
              ),
              child: Text(isEditing ? LocaleKeys.keyUpdate.localized : LocaleKeys.keyAdd.localized, style: TextStyles.regular.copyWith(color: AppColors.primary),),
            ),
          ],
        );
      },
    );
  }

  void _showDeleteConfirmationDialog(BuildContext context, City city) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(LocaleKeys.keyConfirmDelete.localized),
          content: Text('${LocaleKeys.keyDeleteCityMsg.localized} "${city.cityName}"?'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(LocaleKeys.keyCancel.localized),
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              onPressed: () {
                _deleteCity(city.cityId!);
                Navigator.of(context).pop();
              },
              child: Text(LocaleKeys.keyDelete.localized),
            ),
          ],
        );
      },
    );
  }

  void _addNewCity() {
    ref.read(cityManagementController).addCity();
  }

  void _updateCity(int cityId) {
    ref.read(cityManagementController).updateCity(cityId);
  }

  void _deleteCity(int cityId) {
    ref.read(cityManagementController).deleteCity(cityId);
  }

}
