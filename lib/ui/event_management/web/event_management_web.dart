import 'dart:typed_data';

import 'package:ds_admin/framework/utils/extension/extension.dart';
import 'package:ds_admin/framework/utils/extension/string_extension.dart';
import 'package:ds_admin/main.dart';
import 'package:ds_admin/ui/utils/theme/locale_keys.g.dart';
import 'package:ds_admin/ui/utils/widgets/app_toast.dart';
import 'package:ds_admin/ui/utils/widgets/base_scaffold.dart';
import 'package:ds_admin/ui/utils/widgets/dialog_progressbar.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:html_editor_enhanced/html_editor.dart';
import 'package:image_picker_web/image_picker_web.dart';

import '../../../framework/controller/event_management/event_management_controller.dart';
import '../../../framework/repository/event_management/model/get_events_response.dart';
import '../../routing/navigation_stack_item.dart';
import '../../utils/theme/theme.dart';
import '../../utils/widgets/common_text.dart';
import '../../utils/widgets/common_web_header.dart';

class EventManagementWeb extends ConsumerStatefulWidget {
  const EventManagementWeb({super.key});

  @override
  ConsumerState<EventManagementWeb> createState() => _EventManagementWebState();
}

class _EventManagementWebState extends ConsumerState<EventManagementWeb> {
  @override
  void initState() {
    super.initState();
    SchedulerBinding.instance.addPostFrameCallback((_) {
      var controller = ref.read(eventManagementController);
      controller.getEventsListApiCall(controller.currentPage, controller.searchController.text);
    });
  }

  @override
  void dispose() {
    ref.read(eventManagementController).disposeController(isNotify: false);
    super.dispose();
  }

  void _showEventFormDialog({Event? event}) {
    var eventController = ref.watch(eventManagementController);
    eventController.updateEventFormDialog(event);
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        insetPadding: const EdgeInsets.all(16),
        child: Consumer(
          builder: (BuildContext context, WidgetRef ref, Widget? child) {
            var controller = ref.watch(eventManagementController);
            return Stack(
              children: [
                Container(
                  width: MediaQuery.of(context).size.width * 0.7,
                  height: MediaQuery.of(context).size.height * 0.8,
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            eventController.isEditing ? LocaleKeys.keyEditEvent.localized : LocaleKeys.keyAddNewEvent.localized,
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          IconButton(
                            icon: const Icon(Icons.close),
                            onPressed: () => Navigator.pop(context),
                          ),
                        ],
                      ),
                      const Divider(),
                      Expanded(
                        child: SingleChildScrollView(
                          child: Form(
                            key: eventController.formKey,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const SizedBox(height: 16),
                                TextFormField(
                                  controller: eventController.titleController,
                                  decoration: InputDecoration(
                                    labelText: LocaleKeys.keyEventTitle.localized,
                                  ),
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return LocaleKeys.keyErrEventTitle.localized;
                                    }
                                    return null;
                                  },
                                ),
                                const SizedBox(height: 16),
                                GestureDetector(
                                  onTap: () async {
                                    final DateTime? picked = await showDialog(
                                      context: context,
                                      builder: (BuildContext context) {
                                        return Stack(
                                          children: [
                                            Positioned(
                                              top: 30,
                                              left: 0,
                                              right: 0,
                                              child: DatePickerDialog(
                                                initialDate: DateTime.now(),
                                                firstDate: DateTime(DateTime.now().year),
                                                lastDate: DateTime(2030),
                                              ),
                                            ),
                                          ],
                                        );
                                      },
                                    ).then((value) => value as DateTime?);
                                    if (picked != null) {
                                      eventController.updateDatePicked(picked);
                                    }
                                  },
                                  child: AbsorbPointer(
                                    child: TextFormField(
                                      controller: eventController.dateController,
                                      decoration: InputDecoration(
                                        labelText: LocaleKeys.keyEventDate.localized,
                                        suffixIcon: IconButton(
                                          icon: Icon(
                                            Icons.calendar_today,
                                            color: AppColors.primary,
                                          ),
                                          onPressed: null,
                                        ),
                                      ),
                                      readOnly: true,
                                      validator: (value) {
                                        if (value == null || value.isEmpty) {
                                          return LocaleKeys.keyErrEventDate.localized;
                                        }
                                        return null;
                                      },
                                    ),
                                  ),
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  LocaleKeys.keyEventPhoto.localized,
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Row(
                                  children: [
                                    ElevatedButton.icon(
                                      icon: const Icon(Icons.image, color: AppColors.primary),
                                      label: Text(LocaleKeys.keyChooseImage.localized,
                                        style: TextStyle(color: AppColors.primary),
                                      ),
                                      onPressed: () async {
                                        Uint8List? webImage;
                                        await ImagePickerWeb.getImageAsBytes().then((value) => webImage = value);
                                        if (webImage != null) {
                                          eventController.updateImagePicked(webImage);
                                        }
                                      },
                                    ),
                                    const SizedBox(width: 16),
                                    if (controller.selectedImageBytes != null) Text(LocaleKeys.keyNewImageSelected.localized),
                                    if (eventController.imagePreviewUrl.isNotEmpty && eventController.selectedImageBytes == null) Text(LocaleKeys.keyExistingImage.localized),
                                  ],
                                ),
                                const SizedBox(height: 8),
                                if (controller.selectedImageBytes != null)
                                  Container(
                                    height: 150,
                                    width: 250,
                                    decoration: BoxDecoration(
                                      border: Border.all(color: Colors.grey),
                                      borderRadius: BorderRadius.circular(4),
                                    ),
                                    child: Stack(
                                      children: [
                                        Image.memory(controller.selectedImageBytes!, fit: BoxFit.cover),
                                        Positioned(
                                          top: 0,
                                          right: 0,
                                          child: IconButton(
                                            style: ButtonStyle(
                                              backgroundColor: WidgetStateProperty.all(AppColors.greyF6F6F6),
                                              shape: WidgetStateProperty.all(const CircleBorder()),
                                            ),
                                            icon: const Icon(Icons.close, color: Colors.red),
                                            onPressed: () {
                                              eventController.updateImagePicked(null);
                                            },
                                          ),
                                        ),
                                      ],
                                    ),
                                  )
                                else if (eventController.imagePreviewUrl.isNotEmpty)
                                  Container(
                                    height: 150,
                                    width: 250,
                                    decoration: BoxDecoration(
                                      border: Border.all(color: Colors.grey),
                                      borderRadius: BorderRadius.circular(4),
                                    ),
                                    child: Image.network(eventController.imagePreviewUrl, fit: BoxFit.cover),
                                  ),
                                const SizedBox(height: 16),
                                Text(LocaleKeys.keyEventDescription.localized,
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                SizedBox(
                                  height: 200,
                                  child: HtmlEditor(
                                    controller: eventController.htmlEditorController,
                                    htmlEditorOptions: HtmlEditorOptions(
                                      hint: LocaleKeys.keyErrEventDescription.localized,
                                      // initialText: eventController.editorContent,
                                      initialText: eventController.extractTextFromHtml(eventController.editorContent),
                                    ),
                                    htmlToolbarOptions: const HtmlToolbarOptions(
                                      defaultToolbarButtons: [
                                        ColorButtons(),
                                        ParagraphButtons(lineHeight: false, caseConverter: false),
                                        InsertButtons(
                                          link: true,
                                        ),
                                        OtherButtons(),
                                      ],
                                      toolbarType: ToolbarType.nativeGrid,
                                    ),
                                  ),
                                ),
                                const SizedBox(
                                  height: 16,
                                )
                              ],
                            ),
                          ),
                        ),
                      ),
                      const Divider(),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          TextButton(
                            onPressed: () => Navigator.pop(context),
                            child: Text(LocaleKeys.keyCancel.localized, style: TextStyle(color: AppColors.red)),
                          ),
                          const SizedBox(width: 8),
                          ElevatedButton(
                            onPressed: _saveEvent,
                            style: ButtonStyle(
                              backgroundColor: WidgetStateProperty.all(AppColors.greyF6F6F6),
                            ),
                            child: Text(
                              eventController.isEditing ? LocaleKeys.keyUpdateEvent.localized : LocaleKeys.keyAddEvent.localized,
                              style: TextStyle(color: AppColors.primary),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                DialogProgressBar(isLoading: controller.isLoading)
              ],
            );
          },
        ),
      ),
    );
  }

  void _saveEvent() async {
    var eventController = ref.watch(eventManagementController);
    if (eventController.formKey.currentState!.validate()) {
      String description = await eventController.htmlEditorController.getText();
      if (description.isEmpty) {
        AppToast.showSnackBar(
          LocaleKeys.keyErrEventDescription.localized,
          iconImage: Icons.error,
          iconColor: AppColors.redF94008,
          textColor: AppColors.redF94008,
          decorationColor: AppColors.redF94008.withValues(alpha: 0.35),
          snackbarKey.currentState,
              () {
            snackbarKey.currentState?.hideCurrentSnackBar();
          },
        );
        return;
      }

      // if (eventController.imagePreviewUrl.isEmpty || eventController.selectedImageBytes == null) {
      //   AppToast.showSnackBar('Please select an image for the event', snackbarKey.currentState, () {
      //     snackbarKey.currentState?.hideCurrentSnackBar();
      //   });
      //   return;
      // }

      await eventController.saveUpdateEventApiCall();
      Navigator.pop(context);
    }
  }

  void _deleteEvent(Event event) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(LocaleKeys.keyConfirmDelete.localized),
        content: Text('${LocaleKeys.keyConfirmDeleteMsg.localized} "${event.eventTitle}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(LocaleKeys.keyCancel.localized),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ref.read(eventManagementController).deleteEventApiCall(event);
            },
            child: Text(LocaleKeys.keyDelete.localized, style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  Widget _buildEventCard(Event event) {
    var eventController = ref.watch(eventManagementController);
    return Card(
      elevation: 2,
      color: AppColors.greyF6F6F6,
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Event Image
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: InkWell(
                onTap: () {
                  showFullImage(event);
                },
                child: Image.network(
                  event.eventPhoto ?? '',
                  width: 120,
                  height: 120,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      width: 120,
                      height: 120,
                      color: Colors.grey.shade300,
                      child: const Icon(Icons.image_not_supported, size: 40),
                    );
                  },
                ),
              ),
            ),
            const SizedBox(width: 16),
            // Event Details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    event.eventTitle ?? '',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(Icons.calendar_today, size: 16, color: AppColors.primary),
                      const SizedBox(width: 4),
                      Text(
                        event.eventDate ?? '',
                        style: TextStyle(
                          color: Colors.grey.shade700,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  SizedBox(
                    height: 80,
                    child: SingleChildScrollView(
                      child: HtmlWidget(
                        eventController.extractTextFromHtml(event.eventDescription ?? ''),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            // Actions
            Column(
              children: [
                IconButton(
                  icon: const Icon(Icons.edit, color: Colors.blue),
                  onPressed: () => _showEventFormDialog(event: event),
                  tooltip: LocaleKeys.keyEdit.localized,
                ),
                IconButton(
                  icon: const Icon(Icons.delete, color: Colors.red),
                  onPressed: () => _deleteEvent(event),
                  tooltip: LocaleKeys.keyDelete.localized,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void showFullImage(Event event) {
    if (event.eventPhoto != null && event.eventPhoto!.isNotEmpty && Uri.tryParse(event.eventPhoto!)?.hasAbsolutePath == true) {
      showDialog(
        context: context,
        builder: (context) => Dialog(
          insetPadding: EdgeInsets.all(50),
          child: Stack(
            children: [
              InteractiveViewer(
                child: Image.network(
                  event.eventPhoto ?? '',
                  fit: BoxFit.contain,
                  height: double.infinity,
                  width: double.infinity,
                ),
              ),
              Positioned(
                top: 8,
                right: 8,
                child: IconButton(
                  icon: const Icon(Icons.close, color: Colors.white),
                  onPressed: () => Navigator.pop(context),
                  style: ButtonStyle(
                    backgroundColor: WidgetStateProperty.all(AppColors.primary),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    } else {
      AppToast.showSnackBar(
        LocaleKeys.keyImageUnavailable.localized,
        snackbarKey.currentState,
        iconImage: Icons.error,
        iconColor: AppColors.redF94008,
        textColor: AppColors.redF94008,
        decorationColor: AppColors.redF94008.withValues(alpha: 0.35),
        () {
          snackbarKey.currentState?.hideCurrentSnackBar();
        },
      );
    }
  }

  Widget _buildEmptyState() {
    var eventController = ref.watch(eventManagementController);
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.event_busy,
            size: 80,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            LocaleKeys.keyNoEventFound.localized,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade700,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            eventController.events.isEmpty &&  eventController.searchController.text.isEmpty ? LocaleKeys.keyAddNewEventMsg.localized : LocaleKeys.keyTryAdjustingSearch.localized,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 24),
          eventController.events.isEmpty &&  eventController.searchController.text.isEmpty ? ElevatedButton.icon(
            icon: const Icon(Icons.add),
            label: Text(LocaleKeys.keyAddEvent.localized),
            onPressed: () => _showEventFormDialog(),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ): SizedBox(),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    var eventController = ref.watch(eventManagementController);
    return BaseScaffold(
      route: NavigationStackItem.eventManagement(),
      isMobile: false,
      body: Stack(
        children: [
          Container(
            color: AppColors.white,
            height: MediaQuery.of(context).size.height,
            width: MediaQuery.of(context).size.width,
            child: Column(
              children: [
                CommonWebHeader(ref: ref),
                Expanded(
                  child: Card(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Header and Add Button
                        Row(
                          children: [
                            Text(LocaleKeys.keyEventList.localized,
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const Spacer(),
                            ElevatedButton.icon(
                              icon: Icon(
                                Icons.add,
                                color: AppColors.primary,
                              ),
                              label: CommonText(
                                title: LocaleKeys.keyAddEvent.localized,
                                textStyle: TextStyles.regular.copyWith(color: AppColors.primary),
                              ),
                              style: ButtonStyle(
                                backgroundColor: WidgetStateProperty.all(AppColors.greyF6F6F6),
                              ),
                              onPressed: () => _showEventFormDialog(),
                            ),
                          ],
                        ).paddingSymmetric(horizontal: 16, vertical: 16),
                        const SizedBox(height: 16),
                        // Search Bar
                        TextField(
                          controller: eventController.searchController,
                          decoration: InputDecoration(
                            hintText: LocaleKeys.keySearchByName.localized,
                            prefixIcon: Icon(Icons.search),
                            border: OutlineInputBorder(),
                            enabledBorder: OutlineInputBorder(),
                          ),
                          onChanged: (value) {
                            if(value.length > 3){
                              eventController.getEventsListApiCall(eventController.currentPage, eventController.searchController.text);
                            }else if(value.isEmpty){
                              eventController.getEventsListApiCall(1, eventController.searchController.text);
                            }
                          },
                        ).paddingSymmetric(horizontal: 16),
                        const SizedBox(height: 16),
                        // Events List with Pagination
                        Expanded(
                          child: eventController.events.isEmpty
                              ? _buildEmptyState()
                              : Column(
                                  children: [
                                    Expanded(
                                      child: ListView.builder(
                                        itemCount: eventController.events.length,
                                        itemBuilder: (context, index) {
                                          return _buildEventCard(eventController.events[index]).paddingSymmetric(horizontal: 16);
                                        },
                                      ),
                                    ),
                                    // Pagination Controls
                                    Padding(
                                      padding: const EdgeInsets.symmetric(vertical: 8.0),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children: [
                                          IconButton(
                                            icon: const Icon(Icons.chevron_left),
                                            onPressed: (){
                                              if(eventController.eventsResponse.data?.currentPage != 1){
                                                eventController.getEventsListApiCall(eventController.currentPage - 1, eventController.searchController.text);
                                              }
                                            },
                                          ),
                                          Text('${eventController.eventsResponse.data?.currentPage} of ${eventController.eventsResponse.data?.totalPages}'),
                                          IconButton(
                                            icon: const Icon(Icons.chevron_right),
                                            onPressed: (){
                                              if(eventController.eventsResponse.data?.currentPage != eventController.eventsResponse.data?.totalPages){
                                                eventController.getEventsListApiCall(eventController.currentPage + 1, eventController.searchController.text);
                                              }
                                            },
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                        ),
                      ],
                    ),
                  ).paddingSymmetric(horizontal: 20, vertical: 20),
                ),
              ],
            ),
          ),
          DialogProgressBar(isLoading: eventController.isLoading),
        ],
      ),
    );
  }
}
