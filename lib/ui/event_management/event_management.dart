import 'package:responsive_builder/responsive_builder.dart';

import '../utils/theme/theme.dart';
import 'mobile/event_management_mobile.dart';
import 'web/event_management_web.dart';

class EventManagement extends StatefulWidget {
  const EventManagement({super.key});

  @override
  State<EventManagement> createState() => _EventManagementState();
}

class _EventManagementState extends State<EventManagement> {
  @override
  Widget build(BuildContext context) {
    return ScreenTypeLayout.builder(
      mobile: (BuildContext context) => const EventManagementMobile(),
      tablet: (BuildContext context) => const EventManagementWeb(),
      desktop: (BuildContext context) => const EventManagementWeb(),
    );
  }
}
