import 'package:ds_admin/framework/utils/extension/extension.dart';
import 'package:ds_admin/framework/utils/extension/string_extension.dart';
import 'package:ds_admin/ui/dashboard/helper/age_wise_chart.dart';
import 'package:ds_admin/ui/dashboard/helper/dashbord_tile_web.dart';
import 'package:ds_admin/ui/utils/widgets/change_language_widget.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../framework/controller/dashboard/dashboard_controller.dart';
import '../../routing/navigation_stack_item.dart';
import '../../utils/theme/locale_keys.g.dart';
import '../../utils/theme/theme.dart';
import '../../utils/widgets/base_scaffold.dart';
import '../../utils/widgets/common_text.dart';

class DashboardMobile extends ConsumerStatefulWidget {
  const DashboardMobile({super.key});

  @override
  ConsumerState<DashboardMobile> createState() => _DashboardMobileState();
}

class _DashboardMobileState extends ConsumerState<DashboardMobile> {

  @override
  void initState() {
    super.initState();
    SchedulerBinding.instance.addPostFrameCallback((timestamp){
      ref.read(dashboardController).getDashboardData(context: context);
    });
  }

  @override
  Widget build(BuildContext context) {
    var controller = ref.watch(dashboardController);
    return BaseScaffold(
      route: NavigationStackItem.dashboard(),
      isMobile: true,
      appbar: AppBar(
        title: CommonText(title: LocaleKeys.keyDashboard.localized, textStyle: TextStyles.medium.copyWith(color: AppColors.white),),
        centerTitle: true,
        backgroundColor: AppColors.primary,
        actions: [
          ChangeLanguageWidget(ref: ref,).paddingOnly(right: 20),
        ],
      ),
      body: Container(
        color: AppColors.white,
        height: MediaQuery.of(context).size.height-54,
        width: MediaQuery.of(context).size.width,
        child: SingleChildScrollView(
          child: Column(
            children: [
              Wrap(
                runSpacing: 25.h,
                spacing: 20.w,
                children: [
                  DashboardTileWeb(
                    tileKey: LocaleKeys.keyTotalPopulation.localized,
                    tileValue: (controller.dashboardResponse.data?.totalMembers ?? '0').toString(),
                    tileIcon: Icons.groups_sharp,
                  ),
                  DashboardTileWeb(
                    tileKey: LocaleKeys.keyPopulation18PlusMale.localized,
                    tileValue: (controller.dashboardResponse.data?.totalActiveMaleMembers ?? '0').toString(),
                    tileIcon: Icons.male_sharp,
                  ),
                  DashboardTileWeb(
                    tileKey: LocaleKeys.keyPopulation18PlusFemale.localized,
                    tileValue: (controller.dashboardResponse.data?.totalActiveFemaleMembers ?? '0').toString(),
                    tileIcon: Icons.female,
                  ),
                  DashboardTileWeb(
                    tileKey: LocaleKeys.keyTotalCities.localized,
                    tileValue: (controller.dashboardResponse.data?.totalCities ?? '0').toString(),
                    tileIcon: Icons.location_city_sharp,
                  ),
                  DashboardTileWeb(
                    tileKey: LocaleKeys.keyTotalStates.localized,
                    tileValue: (controller.dashboardResponse.data?.totalStates ?? '0').toString(),
                    tileIcon: Icons.location_city_outlined,
                  )
                ],
              ).paddingOnly(left: 20, top: 20, right: 20),
              SizedBox(
                height: 220.h,
                width: 500.h,
                child: AgeWiseChart(),
              ).paddingSymmetric(vertical: 50.h).alignAtCenterLeft()
            ],
          ),
        ),
      ),
    );
  }
}
