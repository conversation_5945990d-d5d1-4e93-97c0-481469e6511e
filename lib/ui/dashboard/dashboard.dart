import 'package:ds_admin/ui/dashboard/mobile/dashboard_mobile.dart';
import 'package:ds_admin/ui/dashboard/web/dashboard_web.dart';
import 'package:responsive_builder/responsive_builder.dart';

import '../utils/theme/theme.dart';

class Dashboard extends StatelessWidget {
  const Dashboard({super.key});

  @override
  Widget build(BuildContext context) {
    return ScreenTypeLayout.builder(
      mobile: (BuildContext context) {
        return const DashboardMobile();
      },
      desktop: (BuildContext context) {
        return const DashboardWeb();
      },
    );
  }
}
