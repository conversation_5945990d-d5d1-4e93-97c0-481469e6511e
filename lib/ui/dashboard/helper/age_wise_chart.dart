import 'package:ds_admin/framework/repository/dashboard/model/dashboard_response.dart';
import 'package:ds_admin/framework/utils/extension/string_extension.dart';
import 'package:ds_admin/ui/dashboard/helper/indicator.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../framework/controller/dashboard/dashboard_controller.dart';
import '../../utils/theme/theme.dart';

class AgeWiseChart extends StatefulWidget {
  const AgeWiseChart({super.key});

  @override
  State<AgeWiseChart> createState() => _AgeWiseChartState();
}

class _AgeWiseChartState extends State<AgeWiseChart> {
  int touchedIndex = -1;

  @override
  Widget build(BuildContext context) {
    return AspectRatio(
      aspectRatio: 1.3,
      child: Row(
        children: <Widget>[
          const SizedBox(
            height: 18,
          ),
          Expanded(
            child: AspectRatio(
              aspectRatio: 1,
              child: Consumer(
                builder: (BuildContext context, WidgetRef ref, Widget? child) {
                  var controller = ref.watch(dashboardController);
                  return PieChart(
                    PieChartData(
                      pieTouchData: PieTouchData(
                        touchCallback: (FlTouchEvent event, pieTouchResponse) {
                          setState(() {
                            if (!event.isInterestedForInteractions ||
                                pieTouchResponse == null ||
                                pieTouchResponse.touchedSection == null) {
                              touchedIndex = -1;
                              return;
                            }
                            touchedIndex = pieTouchResponse
                                .touchedSection!.touchedSectionIndex;
                          });
                        },
                      ),
                      borderData: FlBorderData(
                        show: false,
                      ),
                      sectionsSpace: 0,
                      centerSpaceRadius: 40,
                      sections: showingSections(controller.dashboardResponse.data?.ageWiseChart),
                    ),
                  );
                },
              ),
            ),
          ),
          Consumer(
            builder: (BuildContext context, WidgetRef ref, Widget? child) {
              var controller = ref.watch(dashboardController);
              return Column(
                mainAxisAlignment: MainAxisAlignment.end,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: List.generate(controller.dashboardResponse.data?.ageWiseChart?.length ?? 0, (index) {
                  return Indicator(
                    color: index == 0 ?AppColors.blue75A8F9 : index == 1 ? AppColors.yellowEAFD86 : index == 2 ? AppColors.purple : index == 3 ? AppColors.green : AppColors.greyF6F6F6,
                    text: controller.dashboardResponse.data?.ageWiseChart?[index].ageGroup ?? '',
                    isSquare: true,
                  );
                }).expand((widget) => [widget, const SizedBox(height: 4)]).toList()
                  ..removeLast()
                  ..add(const SizedBox(height: 18)),
              );
            },
          ),
          const SizedBox(
            width: 28,
          ),
        ],
      ),
    );
  }

  List<PieChartSectionData> showingSections(List<AgeWiseChartData>? ageWiseChart) {
    return List.generate(ageWiseChart?.length ?? 0, (i) {
      final isTouched = i == touchedIndex;
      final fontSize = isTouched ? 20.0 : 12.0;
      final radius = isTouched ? 60.0 : 50.0;
      const shadows = [Shadow(color: Colors.black, blurRadius: 2)];
      switch (i) {
        case 0:
          return PieChartSectionData(
            color: AppColors.blue75A8F9,
            value: ageWiseChart?[i].percentage?.parseDouble(),
            title: '${ageWiseChart?[i].percentage}%',
            radius: radius,
            titleStyle: TextStyle(
              fontSize: fontSize,
              fontWeight: FontWeight.bold,
              color: AppColors.white,
              shadows: shadows,
            ),
          );
        case 1:
          return PieChartSectionData(
            color: AppColors.yellowEAFD86,
            value: ageWiseChart?[i].percentage?.parseDouble(),
            title: '${ageWiseChart?[i].percentage}%',
            radius: radius,
            titleStyle: TextStyle(
              fontSize: fontSize,
              fontWeight: FontWeight.bold,
              color: AppColors.white,
              shadows: shadows,
            ),
          );
        case 2:
          return PieChartSectionData(
            color: AppColors.purple,
            value: ageWiseChart?[i].percentage?.parseDouble(),
            title: '${ageWiseChart?[i].percentage}%',
            radius: radius,
            titleStyle: TextStyle(
              fontSize: fontSize,
              fontWeight: FontWeight.bold,
              color: AppColors.white,
              shadows: shadows,
            ),
          );
        case 3:
          return PieChartSectionData(
            color: AppColors.green,
            value: ageWiseChart?[i].percentage?.parseDouble(),
            title: '${ageWiseChart?[i].percentage}%',
            radius: radius,
            titleStyle: TextStyle(
              fontSize: fontSize,
              fontWeight: FontWeight.bold,
              color: AppColors.white,
              shadows: shadows,
            ),
          );
        default:
          throw Error();
      }
    });
  }
}
