import 'package:ds_admin/framework/utils/extension/extension.dart';
import 'package:ds_admin/ui/utils/widgets/common_text.dart';

import '../../utils/theme/theme.dart';

class DashboardTileWeb extends StatelessWidget {
  const DashboardTileWeb({
    super.key,
    required this.tileKey,
    required this.tileValue,
    required this.tileIcon,
  });

  final String tileKey;
  final String tileValue;
  final IconData tileIcon;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 150.h,
      width: 320.w,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.r),
        color: AppColors.white,
        boxShadow: const [
          BoxShadow(
            color: AppColors.ascent, // shadow color
            blurRadius: 10, // shadow radius
            offset: Offset(0, 1), // shadow offset
            spreadRadius: 0.1, // The amount the box should be inflated prior to applying the blur
            blurStyle: BlurStyle.normal,
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: Container(
              margin: EdgeInsets.all(10),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  CommonText(
                    title: tileKey,
                    textStyle: TextStyles.medium.copyWith(fontSize: 16),
                    maxLines: 2,
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(
                    height: 12.h,
                  ),
                  CommonText(
                    title: tileValue,
                    textStyle: TextStyles.extraBold.copyWith(color: AppColors.primary, fontSize: 24),
                  ),
                ],
              ),
            ),
          ),
          Container(
            width: 75.w,
            height: double.infinity,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.only(topRight: Radius.circular(10.r), bottomRight: Radius.circular(10.r)),
              color: AppColors.primary,
            ),
            child: Icon(
              tileIcon,
              color: AppColors.white,
              size: 60.h,
            ),
          ).paddingSymmetric(horizontal: 2.w, vertical: 2.h)
        ],
      ),
    );
  }
}
