import 'package:ds_admin/framework/controller/dashboard/dashboard_controller.dart';
import 'package:ds_admin/framework/utils/extension/extension.dart';
import 'package:ds_admin/framework/utils/extension/string_extension.dart';
import 'package:ds_admin/ui/dashboard/helper/age_wise_chart.dart';
import 'package:ds_admin/ui/dashboard/helper/dashbord_tile_web.dart';
import 'package:ds_admin/ui/routing/navigation_stack_item.dart';
import 'package:ds_admin/ui/utils/theme/locale_keys.g.dart';
import 'package:ds_admin/ui/utils/widgets/base_scaffold.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../utils/theme/theme.dart';
import '../../utils/widgets/common_web_header.dart';

class DashboardWeb extends ConsumerStatefulWidget {
  const DashboardWeb({super.key});

  @override
  ConsumerState<DashboardWeb> createState() => _DashboardWebState();
}

class _DashboardWebState extends ConsumerState<DashboardWeb> {

  @override
  void initState() {
    super.initState();
    SchedulerBinding.instance.addPostFrameCallback((timestamp){
      ref.read(dashboardController).getDashboardData(context: context);
    });
  }

  @override
  Widget build(BuildContext context) {
    var controller = ref.watch(dashboardController);
    return BaseScaffold(
      route: NavigationStackItem.dashboard(),
      isMobile: false,
      body: Container(
        color: AppColors.white,
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        child: Column(
          children: [
            CommonWebHeader(ref: ref),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    Wrap(
                      runSpacing: 25.h,
                      spacing: 20.w,
                      children: [
                        DashboardTileWeb(
                          tileKey: LocaleKeys.keyTotalPopulation.localized,
                          tileValue: (controller.dashboardResponse.data?.totalMembers ?? '0').toString(),
                          tileIcon: Icons.groups_sharp,
                        ),
                        DashboardTileWeb(
                          tileKey: LocaleKeys.keyPopulation18PlusMale.localized,
                          tileValue: (controller.dashboardResponse.data?.totalActiveMaleMembers ?? '0').toString(),
                          tileIcon: Icons.male_sharp,
                        ),
                        DashboardTileWeb(
                          tileKey: LocaleKeys.keyPopulation18PlusFemale.localized,
                          tileValue: (controller.dashboardResponse.data?.totalActiveFemaleMembers ?? '0').toString(),
                          tileIcon: Icons.female,
                        ),
                        DashboardTileWeb(
                          tileKey: LocaleKeys.keyTotalCities.localized,
                          tileValue: (controller.dashboardResponse.data?.totalCities ?? '0').toString(),
                          tileIcon: Icons.location_city_sharp,
                        ),
                        DashboardTileWeb(
                          tileKey: LocaleKeys.keyTotalStates.localized,
                          tileValue: (controller.dashboardResponse.data?.totalStates ?? '0').toString(),
                          tileIcon: Icons.location_city_outlined,
                        )
                      ],
                    ).paddingOnly(left: 20, top: 20, right: 20),
                    SizedBox(
                      height: 220.h,
                      width: 500.h,
                      child: AgeWiseChart(),
                    ).paddingSymmetric(vertical: 50.h).alignAtCenterLeft()
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
