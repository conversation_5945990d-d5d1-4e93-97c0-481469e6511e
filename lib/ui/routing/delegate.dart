import 'package:ds_admin/ui/dashboard/dashboard.dart';
import 'package:ds_admin/ui/login/login.dart';
import 'package:ds_admin/ui/member_management/member_management.dart';
import 'package:ds_admin/ui/routing/navigation_stack_keys.dart';
import 'package:ds_admin/ui/routing/stack.dart';
import 'package:ds_admin/ui/rules_management/rules_management.dart';
import 'package:ds_admin/ui/splash/splash.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:injectable/injectable.dart';

import '../about_management/about_management.dart';
import '../city_management/city_management.dart';
import '../event_management/event_management.dart';
import '../state_management/state_management.dart';


final globalNavigatorKey = GlobalKey<NavigatorState>();

@injectable
class MainRouterDelegate extends RouterDelegate<NavigationStack>
    with ChangeNotifier, PopNavigatorRouterDelegateMixin {
  final NavigationStack stack;

  @override
  void dispose() {
    stack.removeListener(notifyListeners);
    super.dispose();
  }



  MainRouterDelegate(@factoryParam this.stack) : super() {
    stack.addListener(notifyListeners);
  }

  @override
  final navigatorKey = globalNavigatorKey;

  @override
  Widget build(BuildContext context) {
    return Consumer(builder: (context, ref, child) {
      return Navigator(
        key: navigatorKey,
        pages: _pages(ref),
        /// callback when a page is popped.
        onPopPage: (route, result) {
          /// let the OS handle the back press if there was nothing to pop
          if (!route.didPop(result)) {
            return false;
          }

          /// if we are on root, let OS close app
          if (stack.items.length == 1) return false;

          /// if we are on root, let OS close app
          if (stack.items.isEmpty) return false;

          /// otherwise, pop the stack and consume the event
          stack.pop();
          return true;
        },
      );
    });
  }

  List<Page> _pages(WidgetRef ref) => stack.items.mapIndexed((e, i) => e.when(

    splash: () => const NoAnimationPage(child: Splash(), key: ValueKey(Keys.splash)),
    login: () => const NoAnimationPage(child: Login(), key: ValueKey(Keys.login)),
    dashboard: () => const NoAnimationPage(child: Dashboard(), key: ValueKey(Keys.dashboard)),
    memberManagement: () => const NoAnimationPage(child: MemberManagement(), key: ValueKey(Keys.memberManagement)),
    eventManagement: () => const NoAnimationPage(child: EventManagement(), key: ValueKey(Keys.eventManagement)),
    stateManagement: () => const NoAnimationPage(child: StateManagement(), key: ValueKey(Keys.stateManagement)),
    cityManagement: () => const NoAnimationPage(child: CityManagement(), key: ValueKey(Keys.cityManagement)),
    rulesManagement: () => const NoAnimationPage(child: RulesManagement(), key: ValueKey(Keys.rulesManagement)),
    aboutManagement: () => const NoAnimationPage(child: AboutManagement(), key: ValueKey(Keys.aboutManagement)),

  )).toList();

  @override
  NavigationStack get currentConfiguration => stack;

  @override
  Future<void> setNewRoutePath(NavigationStack configuration) async {
    stack.items = configuration.items;
  }
}

class NoAnimationPage<T> extends Page<T> {
  final Widget child;

  const NoAnimationPage({required this.child, required super.key});

  @override
  Route<T> createRoute(BuildContext context) {
    return PageRouteBuilder<T>(
      settings: this,
      pageBuilder: (context, animation, secondaryAnimation) => child,
      transitionDuration: Duration.zero, // Removes push animation
      reverseTransitionDuration: Duration.zero, // Removes pop animation
    );
  }
}


extension _IndexedIterable<E> on Iterable<E> {
  Iterable<T> mapIndexed<T>(T Function(E e, int i) f) {
    var i = 0;
    return map((e) => f(e, i++));
  }
}
