// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'navigation_stack_item.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$NavigationStackItem {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() splash,
    required TResult Function() login,
    required TResult Function() dashboard,
    required TResult Function() memberManagement,
    required TResult Function() eventManagement,
    required TResult Function() stateManagement,
    required TResult Function() cityManagement,
    required TResult Function() rulesManagement,
    required TResult Function() aboutManagement,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? splash,
    TResult? Function()? login,
    TResult? Function()? dashboard,
    TResult? Function()? memberManagement,
    TResult? Function()? eventManagement,
    TResult? Function()? stateManagement,
    TResult? Function()? cityManagement,
    TResult? Function()? rulesManagement,
    TResult? Function()? aboutManagement,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? splash,
    TResult Function()? login,
    TResult Function()? dashboard,
    TResult Function()? memberManagement,
    TResult Function()? eventManagement,
    TResult Function()? stateManagement,
    TResult Function()? cityManagement,
    TResult Function()? rulesManagement,
    TResult Function()? aboutManagement,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NavigationStackItemSplashPage value) splash,
    required TResult Function(NavigationStackItemLoginPage value) login,
    required TResult Function(NavigationStackItemDashboardScreen value)
        dashboard,
    required TResult Function(NavigationStackItemMemberManagementScreen value)
        memberManagement,
    required TResult Function(NavigationStackItemEventManagementScreen value)
        eventManagement,
    required TResult Function(NavigationStackItemStateManagementScreen value)
        stateManagement,
    required TResult Function(NavigationStackItemCityManagementScreen value)
        cityManagement,
    required TResult Function(NavigationStackItemRulesManagementScreen value)
        rulesManagement,
    required TResult Function(NavigationStackItemAboutManagementScreen value)
        aboutManagement,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NavigationStackItemSplashPage value)? splash,
    TResult? Function(NavigationStackItemLoginPage value)? login,
    TResult? Function(NavigationStackItemDashboardScreen value)? dashboard,
    TResult? Function(NavigationStackItemMemberManagementScreen value)?
        memberManagement,
    TResult? Function(NavigationStackItemEventManagementScreen value)?
        eventManagement,
    TResult? Function(NavigationStackItemStateManagementScreen value)?
        stateManagement,
    TResult? Function(NavigationStackItemCityManagementScreen value)?
        cityManagement,
    TResult? Function(NavigationStackItemRulesManagementScreen value)?
        rulesManagement,
    TResult? Function(NavigationStackItemAboutManagementScreen value)?
        aboutManagement,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NavigationStackItemSplashPage value)? splash,
    TResult Function(NavigationStackItemLoginPage value)? login,
    TResult Function(NavigationStackItemDashboardScreen value)? dashboard,
    TResult Function(NavigationStackItemMemberManagementScreen value)?
        memberManagement,
    TResult Function(NavigationStackItemEventManagementScreen value)?
        eventManagement,
    TResult Function(NavigationStackItemStateManagementScreen value)?
        stateManagement,
    TResult Function(NavigationStackItemCityManagementScreen value)?
        cityManagement,
    TResult Function(NavigationStackItemRulesManagementScreen value)?
        rulesManagement,
    TResult Function(NavigationStackItemAboutManagementScreen value)?
        aboutManagement,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NavigationStackItemCopyWith<$Res> {
  factory $NavigationStackItemCopyWith(
          NavigationStackItem value, $Res Function(NavigationStackItem) then) =
      _$NavigationStackItemCopyWithImpl<$Res, NavigationStackItem>;
}

/// @nodoc
class _$NavigationStackItemCopyWithImpl<$Res, $Val extends NavigationStackItem>
    implements $NavigationStackItemCopyWith<$Res> {
  _$NavigationStackItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of NavigationStackItem
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$NavigationStackItemSplashPageImplCopyWith<$Res> {
  factory _$$NavigationStackItemSplashPageImplCopyWith(
          _$NavigationStackItemSplashPageImpl value,
          $Res Function(_$NavigationStackItemSplashPageImpl) then) =
      __$$NavigationStackItemSplashPageImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$NavigationStackItemSplashPageImplCopyWithImpl<$Res>
    extends _$NavigationStackItemCopyWithImpl<$Res,
        _$NavigationStackItemSplashPageImpl>
    implements _$$NavigationStackItemSplashPageImplCopyWith<$Res> {
  __$$NavigationStackItemSplashPageImplCopyWithImpl(
      _$NavigationStackItemSplashPageImpl _value,
      $Res Function(_$NavigationStackItemSplashPageImpl) _then)
      : super(_value, _then);

  /// Create a copy of NavigationStackItem
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$NavigationStackItemSplashPageImpl
    with DiagnosticableTreeMixin
    implements NavigationStackItemSplashPage {
  const _$NavigationStackItemSplashPageImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'NavigationStackItem.splash()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties..add(DiagnosticsProperty('type', 'NavigationStackItem.splash'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NavigationStackItemSplashPageImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() splash,
    required TResult Function() login,
    required TResult Function() dashboard,
    required TResult Function() memberManagement,
    required TResult Function() eventManagement,
    required TResult Function() stateManagement,
    required TResult Function() cityManagement,
    required TResult Function() rulesManagement,
    required TResult Function() aboutManagement,
  }) {
    return splash();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? splash,
    TResult? Function()? login,
    TResult? Function()? dashboard,
    TResult? Function()? memberManagement,
    TResult? Function()? eventManagement,
    TResult? Function()? stateManagement,
    TResult? Function()? cityManagement,
    TResult? Function()? rulesManagement,
    TResult? Function()? aboutManagement,
  }) {
    return splash?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? splash,
    TResult Function()? login,
    TResult Function()? dashboard,
    TResult Function()? memberManagement,
    TResult Function()? eventManagement,
    TResult Function()? stateManagement,
    TResult Function()? cityManagement,
    TResult Function()? rulesManagement,
    TResult Function()? aboutManagement,
    required TResult orElse(),
  }) {
    if (splash != null) {
      return splash();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NavigationStackItemSplashPage value) splash,
    required TResult Function(NavigationStackItemLoginPage value) login,
    required TResult Function(NavigationStackItemDashboardScreen value)
        dashboard,
    required TResult Function(NavigationStackItemMemberManagementScreen value)
        memberManagement,
    required TResult Function(NavigationStackItemEventManagementScreen value)
        eventManagement,
    required TResult Function(NavigationStackItemStateManagementScreen value)
        stateManagement,
    required TResult Function(NavigationStackItemCityManagementScreen value)
        cityManagement,
    required TResult Function(NavigationStackItemRulesManagementScreen value)
        rulesManagement,
    required TResult Function(NavigationStackItemAboutManagementScreen value)
        aboutManagement,
  }) {
    return splash(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NavigationStackItemSplashPage value)? splash,
    TResult? Function(NavigationStackItemLoginPage value)? login,
    TResult? Function(NavigationStackItemDashboardScreen value)? dashboard,
    TResult? Function(NavigationStackItemMemberManagementScreen value)?
        memberManagement,
    TResult? Function(NavigationStackItemEventManagementScreen value)?
        eventManagement,
    TResult? Function(NavigationStackItemStateManagementScreen value)?
        stateManagement,
    TResult? Function(NavigationStackItemCityManagementScreen value)?
        cityManagement,
    TResult? Function(NavigationStackItemRulesManagementScreen value)?
        rulesManagement,
    TResult? Function(NavigationStackItemAboutManagementScreen value)?
        aboutManagement,
  }) {
    return splash?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NavigationStackItemSplashPage value)? splash,
    TResult Function(NavigationStackItemLoginPage value)? login,
    TResult Function(NavigationStackItemDashboardScreen value)? dashboard,
    TResult Function(NavigationStackItemMemberManagementScreen value)?
        memberManagement,
    TResult Function(NavigationStackItemEventManagementScreen value)?
        eventManagement,
    TResult Function(NavigationStackItemStateManagementScreen value)?
        stateManagement,
    TResult Function(NavigationStackItemCityManagementScreen value)?
        cityManagement,
    TResult Function(NavigationStackItemRulesManagementScreen value)?
        rulesManagement,
    TResult Function(NavigationStackItemAboutManagementScreen value)?
        aboutManagement,
    required TResult orElse(),
  }) {
    if (splash != null) {
      return splash(this);
    }
    return orElse();
  }
}

abstract class NavigationStackItemSplashPage implements NavigationStackItem {
  const factory NavigationStackItemSplashPage() =
      _$NavigationStackItemSplashPageImpl;
}

/// @nodoc
abstract class _$$NavigationStackItemLoginPageImplCopyWith<$Res> {
  factory _$$NavigationStackItemLoginPageImplCopyWith(
          _$NavigationStackItemLoginPageImpl value,
          $Res Function(_$NavigationStackItemLoginPageImpl) then) =
      __$$NavigationStackItemLoginPageImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$NavigationStackItemLoginPageImplCopyWithImpl<$Res>
    extends _$NavigationStackItemCopyWithImpl<$Res,
        _$NavigationStackItemLoginPageImpl>
    implements _$$NavigationStackItemLoginPageImplCopyWith<$Res> {
  __$$NavigationStackItemLoginPageImplCopyWithImpl(
      _$NavigationStackItemLoginPageImpl _value,
      $Res Function(_$NavigationStackItemLoginPageImpl) _then)
      : super(_value, _then);

  /// Create a copy of NavigationStackItem
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$NavigationStackItemLoginPageImpl
    with DiagnosticableTreeMixin
    implements NavigationStackItemLoginPage {
  const _$NavigationStackItemLoginPageImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'NavigationStackItem.login()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties..add(DiagnosticsProperty('type', 'NavigationStackItem.login'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NavigationStackItemLoginPageImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() splash,
    required TResult Function() login,
    required TResult Function() dashboard,
    required TResult Function() memberManagement,
    required TResult Function() eventManagement,
    required TResult Function() stateManagement,
    required TResult Function() cityManagement,
    required TResult Function() rulesManagement,
    required TResult Function() aboutManagement,
  }) {
    return login();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? splash,
    TResult? Function()? login,
    TResult? Function()? dashboard,
    TResult? Function()? memberManagement,
    TResult? Function()? eventManagement,
    TResult? Function()? stateManagement,
    TResult? Function()? cityManagement,
    TResult? Function()? rulesManagement,
    TResult? Function()? aboutManagement,
  }) {
    return login?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? splash,
    TResult Function()? login,
    TResult Function()? dashboard,
    TResult Function()? memberManagement,
    TResult Function()? eventManagement,
    TResult Function()? stateManagement,
    TResult Function()? cityManagement,
    TResult Function()? rulesManagement,
    TResult Function()? aboutManagement,
    required TResult orElse(),
  }) {
    if (login != null) {
      return login();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NavigationStackItemSplashPage value) splash,
    required TResult Function(NavigationStackItemLoginPage value) login,
    required TResult Function(NavigationStackItemDashboardScreen value)
        dashboard,
    required TResult Function(NavigationStackItemMemberManagementScreen value)
        memberManagement,
    required TResult Function(NavigationStackItemEventManagementScreen value)
        eventManagement,
    required TResult Function(NavigationStackItemStateManagementScreen value)
        stateManagement,
    required TResult Function(NavigationStackItemCityManagementScreen value)
        cityManagement,
    required TResult Function(NavigationStackItemRulesManagementScreen value)
        rulesManagement,
    required TResult Function(NavigationStackItemAboutManagementScreen value)
        aboutManagement,
  }) {
    return login(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NavigationStackItemSplashPage value)? splash,
    TResult? Function(NavigationStackItemLoginPage value)? login,
    TResult? Function(NavigationStackItemDashboardScreen value)? dashboard,
    TResult? Function(NavigationStackItemMemberManagementScreen value)?
        memberManagement,
    TResult? Function(NavigationStackItemEventManagementScreen value)?
        eventManagement,
    TResult? Function(NavigationStackItemStateManagementScreen value)?
        stateManagement,
    TResult? Function(NavigationStackItemCityManagementScreen value)?
        cityManagement,
    TResult? Function(NavigationStackItemRulesManagementScreen value)?
        rulesManagement,
    TResult? Function(NavigationStackItemAboutManagementScreen value)?
        aboutManagement,
  }) {
    return login?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NavigationStackItemSplashPage value)? splash,
    TResult Function(NavigationStackItemLoginPage value)? login,
    TResult Function(NavigationStackItemDashboardScreen value)? dashboard,
    TResult Function(NavigationStackItemMemberManagementScreen value)?
        memberManagement,
    TResult Function(NavigationStackItemEventManagementScreen value)?
        eventManagement,
    TResult Function(NavigationStackItemStateManagementScreen value)?
        stateManagement,
    TResult Function(NavigationStackItemCityManagementScreen value)?
        cityManagement,
    TResult Function(NavigationStackItemRulesManagementScreen value)?
        rulesManagement,
    TResult Function(NavigationStackItemAboutManagementScreen value)?
        aboutManagement,
    required TResult orElse(),
  }) {
    if (login != null) {
      return login(this);
    }
    return orElse();
  }
}

abstract class NavigationStackItemLoginPage implements NavigationStackItem {
  const factory NavigationStackItemLoginPage() =
      _$NavigationStackItemLoginPageImpl;
}

/// @nodoc
abstract class _$$NavigationStackItemDashboardScreenImplCopyWith<$Res> {
  factory _$$NavigationStackItemDashboardScreenImplCopyWith(
          _$NavigationStackItemDashboardScreenImpl value,
          $Res Function(_$NavigationStackItemDashboardScreenImpl) then) =
      __$$NavigationStackItemDashboardScreenImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$NavigationStackItemDashboardScreenImplCopyWithImpl<$Res>
    extends _$NavigationStackItemCopyWithImpl<$Res,
        _$NavigationStackItemDashboardScreenImpl>
    implements _$$NavigationStackItemDashboardScreenImplCopyWith<$Res> {
  __$$NavigationStackItemDashboardScreenImplCopyWithImpl(
      _$NavigationStackItemDashboardScreenImpl _value,
      $Res Function(_$NavigationStackItemDashboardScreenImpl) _then)
      : super(_value, _then);

  /// Create a copy of NavigationStackItem
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$NavigationStackItemDashboardScreenImpl
    with DiagnosticableTreeMixin
    implements NavigationStackItemDashboardScreen {
  const _$NavigationStackItemDashboardScreenImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'NavigationStackItem.dashboard()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'NavigationStackItem.dashboard'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NavigationStackItemDashboardScreenImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() splash,
    required TResult Function() login,
    required TResult Function() dashboard,
    required TResult Function() memberManagement,
    required TResult Function() eventManagement,
    required TResult Function() stateManagement,
    required TResult Function() cityManagement,
    required TResult Function() rulesManagement,
    required TResult Function() aboutManagement,
  }) {
    return dashboard();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? splash,
    TResult? Function()? login,
    TResult? Function()? dashboard,
    TResult? Function()? memberManagement,
    TResult? Function()? eventManagement,
    TResult? Function()? stateManagement,
    TResult? Function()? cityManagement,
    TResult? Function()? rulesManagement,
    TResult? Function()? aboutManagement,
  }) {
    return dashboard?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? splash,
    TResult Function()? login,
    TResult Function()? dashboard,
    TResult Function()? memberManagement,
    TResult Function()? eventManagement,
    TResult Function()? stateManagement,
    TResult Function()? cityManagement,
    TResult Function()? rulesManagement,
    TResult Function()? aboutManagement,
    required TResult orElse(),
  }) {
    if (dashboard != null) {
      return dashboard();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NavigationStackItemSplashPage value) splash,
    required TResult Function(NavigationStackItemLoginPage value) login,
    required TResult Function(NavigationStackItemDashboardScreen value)
        dashboard,
    required TResult Function(NavigationStackItemMemberManagementScreen value)
        memberManagement,
    required TResult Function(NavigationStackItemEventManagementScreen value)
        eventManagement,
    required TResult Function(NavigationStackItemStateManagementScreen value)
        stateManagement,
    required TResult Function(NavigationStackItemCityManagementScreen value)
        cityManagement,
    required TResult Function(NavigationStackItemRulesManagementScreen value)
        rulesManagement,
    required TResult Function(NavigationStackItemAboutManagementScreen value)
        aboutManagement,
  }) {
    return dashboard(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NavigationStackItemSplashPage value)? splash,
    TResult? Function(NavigationStackItemLoginPage value)? login,
    TResult? Function(NavigationStackItemDashboardScreen value)? dashboard,
    TResult? Function(NavigationStackItemMemberManagementScreen value)?
        memberManagement,
    TResult? Function(NavigationStackItemEventManagementScreen value)?
        eventManagement,
    TResult? Function(NavigationStackItemStateManagementScreen value)?
        stateManagement,
    TResult? Function(NavigationStackItemCityManagementScreen value)?
        cityManagement,
    TResult? Function(NavigationStackItemRulesManagementScreen value)?
        rulesManagement,
    TResult? Function(NavigationStackItemAboutManagementScreen value)?
        aboutManagement,
  }) {
    return dashboard?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NavigationStackItemSplashPage value)? splash,
    TResult Function(NavigationStackItemLoginPage value)? login,
    TResult Function(NavigationStackItemDashboardScreen value)? dashboard,
    TResult Function(NavigationStackItemMemberManagementScreen value)?
        memberManagement,
    TResult Function(NavigationStackItemEventManagementScreen value)?
        eventManagement,
    TResult Function(NavigationStackItemStateManagementScreen value)?
        stateManagement,
    TResult Function(NavigationStackItemCityManagementScreen value)?
        cityManagement,
    TResult Function(NavigationStackItemRulesManagementScreen value)?
        rulesManagement,
    TResult Function(NavigationStackItemAboutManagementScreen value)?
        aboutManagement,
    required TResult orElse(),
  }) {
    if (dashboard != null) {
      return dashboard(this);
    }
    return orElse();
  }
}

abstract class NavigationStackItemDashboardScreen
    implements NavigationStackItem {
  const factory NavigationStackItemDashboardScreen() =
      _$NavigationStackItemDashboardScreenImpl;
}

/// @nodoc
abstract class _$$NavigationStackItemMemberManagementScreenImplCopyWith<$Res> {
  factory _$$NavigationStackItemMemberManagementScreenImplCopyWith(
          _$NavigationStackItemMemberManagementScreenImpl value,
          $Res Function(_$NavigationStackItemMemberManagementScreenImpl) then) =
      __$$NavigationStackItemMemberManagementScreenImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$NavigationStackItemMemberManagementScreenImplCopyWithImpl<$Res>
    extends _$NavigationStackItemCopyWithImpl<$Res,
        _$NavigationStackItemMemberManagementScreenImpl>
    implements _$$NavigationStackItemMemberManagementScreenImplCopyWith<$Res> {
  __$$NavigationStackItemMemberManagementScreenImplCopyWithImpl(
      _$NavigationStackItemMemberManagementScreenImpl _value,
      $Res Function(_$NavigationStackItemMemberManagementScreenImpl) _then)
      : super(_value, _then);

  /// Create a copy of NavigationStackItem
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$NavigationStackItemMemberManagementScreenImpl
    with DiagnosticableTreeMixin
    implements NavigationStackItemMemberManagementScreen {
  const _$NavigationStackItemMemberManagementScreenImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'NavigationStackItem.memberManagement()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(
          DiagnosticsProperty('type', 'NavigationStackItem.memberManagement'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NavigationStackItemMemberManagementScreenImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() splash,
    required TResult Function() login,
    required TResult Function() dashboard,
    required TResult Function() memberManagement,
    required TResult Function() eventManagement,
    required TResult Function() stateManagement,
    required TResult Function() cityManagement,
    required TResult Function() rulesManagement,
    required TResult Function() aboutManagement,
  }) {
    return memberManagement();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? splash,
    TResult? Function()? login,
    TResult? Function()? dashboard,
    TResult? Function()? memberManagement,
    TResult? Function()? eventManagement,
    TResult? Function()? stateManagement,
    TResult? Function()? cityManagement,
    TResult? Function()? rulesManagement,
    TResult? Function()? aboutManagement,
  }) {
    return memberManagement?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? splash,
    TResult Function()? login,
    TResult Function()? dashboard,
    TResult Function()? memberManagement,
    TResult Function()? eventManagement,
    TResult Function()? stateManagement,
    TResult Function()? cityManagement,
    TResult Function()? rulesManagement,
    TResult Function()? aboutManagement,
    required TResult orElse(),
  }) {
    if (memberManagement != null) {
      return memberManagement();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NavigationStackItemSplashPage value) splash,
    required TResult Function(NavigationStackItemLoginPage value) login,
    required TResult Function(NavigationStackItemDashboardScreen value)
        dashboard,
    required TResult Function(NavigationStackItemMemberManagementScreen value)
        memberManagement,
    required TResult Function(NavigationStackItemEventManagementScreen value)
        eventManagement,
    required TResult Function(NavigationStackItemStateManagementScreen value)
        stateManagement,
    required TResult Function(NavigationStackItemCityManagementScreen value)
        cityManagement,
    required TResult Function(NavigationStackItemRulesManagementScreen value)
        rulesManagement,
    required TResult Function(NavigationStackItemAboutManagementScreen value)
        aboutManagement,
  }) {
    return memberManagement(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NavigationStackItemSplashPage value)? splash,
    TResult? Function(NavigationStackItemLoginPage value)? login,
    TResult? Function(NavigationStackItemDashboardScreen value)? dashboard,
    TResult? Function(NavigationStackItemMemberManagementScreen value)?
        memberManagement,
    TResult? Function(NavigationStackItemEventManagementScreen value)?
        eventManagement,
    TResult? Function(NavigationStackItemStateManagementScreen value)?
        stateManagement,
    TResult? Function(NavigationStackItemCityManagementScreen value)?
        cityManagement,
    TResult? Function(NavigationStackItemRulesManagementScreen value)?
        rulesManagement,
    TResult? Function(NavigationStackItemAboutManagementScreen value)?
        aboutManagement,
  }) {
    return memberManagement?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NavigationStackItemSplashPage value)? splash,
    TResult Function(NavigationStackItemLoginPage value)? login,
    TResult Function(NavigationStackItemDashboardScreen value)? dashboard,
    TResult Function(NavigationStackItemMemberManagementScreen value)?
        memberManagement,
    TResult Function(NavigationStackItemEventManagementScreen value)?
        eventManagement,
    TResult Function(NavigationStackItemStateManagementScreen value)?
        stateManagement,
    TResult Function(NavigationStackItemCityManagementScreen value)?
        cityManagement,
    TResult Function(NavigationStackItemRulesManagementScreen value)?
        rulesManagement,
    TResult Function(NavigationStackItemAboutManagementScreen value)?
        aboutManagement,
    required TResult orElse(),
  }) {
    if (memberManagement != null) {
      return memberManagement(this);
    }
    return orElse();
  }
}

abstract class NavigationStackItemMemberManagementScreen
    implements NavigationStackItem {
  const factory NavigationStackItemMemberManagementScreen() =
      _$NavigationStackItemMemberManagementScreenImpl;
}

/// @nodoc
abstract class _$$NavigationStackItemEventManagementScreenImplCopyWith<$Res> {
  factory _$$NavigationStackItemEventManagementScreenImplCopyWith(
          _$NavigationStackItemEventManagementScreenImpl value,
          $Res Function(_$NavigationStackItemEventManagementScreenImpl) then) =
      __$$NavigationStackItemEventManagementScreenImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$NavigationStackItemEventManagementScreenImplCopyWithImpl<$Res>
    extends _$NavigationStackItemCopyWithImpl<$Res,
        _$NavigationStackItemEventManagementScreenImpl>
    implements _$$NavigationStackItemEventManagementScreenImplCopyWith<$Res> {
  __$$NavigationStackItemEventManagementScreenImplCopyWithImpl(
      _$NavigationStackItemEventManagementScreenImpl _value,
      $Res Function(_$NavigationStackItemEventManagementScreenImpl) _then)
      : super(_value, _then);

  /// Create a copy of NavigationStackItem
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$NavigationStackItemEventManagementScreenImpl
    with DiagnosticableTreeMixin
    implements NavigationStackItemEventManagementScreen {
  const _$NavigationStackItemEventManagementScreenImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'NavigationStackItem.eventManagement()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'NavigationStackItem.eventManagement'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NavigationStackItemEventManagementScreenImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() splash,
    required TResult Function() login,
    required TResult Function() dashboard,
    required TResult Function() memberManagement,
    required TResult Function() eventManagement,
    required TResult Function() stateManagement,
    required TResult Function() cityManagement,
    required TResult Function() rulesManagement,
    required TResult Function() aboutManagement,
  }) {
    return eventManagement();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? splash,
    TResult? Function()? login,
    TResult? Function()? dashboard,
    TResult? Function()? memberManagement,
    TResult? Function()? eventManagement,
    TResult? Function()? stateManagement,
    TResult? Function()? cityManagement,
    TResult? Function()? rulesManagement,
    TResult? Function()? aboutManagement,
  }) {
    return eventManagement?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? splash,
    TResult Function()? login,
    TResult Function()? dashboard,
    TResult Function()? memberManagement,
    TResult Function()? eventManagement,
    TResult Function()? stateManagement,
    TResult Function()? cityManagement,
    TResult Function()? rulesManagement,
    TResult Function()? aboutManagement,
    required TResult orElse(),
  }) {
    if (eventManagement != null) {
      return eventManagement();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NavigationStackItemSplashPage value) splash,
    required TResult Function(NavigationStackItemLoginPage value) login,
    required TResult Function(NavigationStackItemDashboardScreen value)
        dashboard,
    required TResult Function(NavigationStackItemMemberManagementScreen value)
        memberManagement,
    required TResult Function(NavigationStackItemEventManagementScreen value)
        eventManagement,
    required TResult Function(NavigationStackItemStateManagementScreen value)
        stateManagement,
    required TResult Function(NavigationStackItemCityManagementScreen value)
        cityManagement,
    required TResult Function(NavigationStackItemRulesManagementScreen value)
        rulesManagement,
    required TResult Function(NavigationStackItemAboutManagementScreen value)
        aboutManagement,
  }) {
    return eventManagement(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NavigationStackItemSplashPage value)? splash,
    TResult? Function(NavigationStackItemLoginPage value)? login,
    TResult? Function(NavigationStackItemDashboardScreen value)? dashboard,
    TResult? Function(NavigationStackItemMemberManagementScreen value)?
        memberManagement,
    TResult? Function(NavigationStackItemEventManagementScreen value)?
        eventManagement,
    TResult? Function(NavigationStackItemStateManagementScreen value)?
        stateManagement,
    TResult? Function(NavigationStackItemCityManagementScreen value)?
        cityManagement,
    TResult? Function(NavigationStackItemRulesManagementScreen value)?
        rulesManagement,
    TResult? Function(NavigationStackItemAboutManagementScreen value)?
        aboutManagement,
  }) {
    return eventManagement?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NavigationStackItemSplashPage value)? splash,
    TResult Function(NavigationStackItemLoginPage value)? login,
    TResult Function(NavigationStackItemDashboardScreen value)? dashboard,
    TResult Function(NavigationStackItemMemberManagementScreen value)?
        memberManagement,
    TResult Function(NavigationStackItemEventManagementScreen value)?
        eventManagement,
    TResult Function(NavigationStackItemStateManagementScreen value)?
        stateManagement,
    TResult Function(NavigationStackItemCityManagementScreen value)?
        cityManagement,
    TResult Function(NavigationStackItemRulesManagementScreen value)?
        rulesManagement,
    TResult Function(NavigationStackItemAboutManagementScreen value)?
        aboutManagement,
    required TResult orElse(),
  }) {
    if (eventManagement != null) {
      return eventManagement(this);
    }
    return orElse();
  }
}

abstract class NavigationStackItemEventManagementScreen
    implements NavigationStackItem {
  const factory NavigationStackItemEventManagementScreen() =
      _$NavigationStackItemEventManagementScreenImpl;
}

/// @nodoc
abstract class _$$NavigationStackItemStateManagementScreenImplCopyWith<$Res> {
  factory _$$NavigationStackItemStateManagementScreenImplCopyWith(
          _$NavigationStackItemStateManagementScreenImpl value,
          $Res Function(_$NavigationStackItemStateManagementScreenImpl) then) =
      __$$NavigationStackItemStateManagementScreenImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$NavigationStackItemStateManagementScreenImplCopyWithImpl<$Res>
    extends _$NavigationStackItemCopyWithImpl<$Res,
        _$NavigationStackItemStateManagementScreenImpl>
    implements _$$NavigationStackItemStateManagementScreenImplCopyWith<$Res> {
  __$$NavigationStackItemStateManagementScreenImplCopyWithImpl(
      _$NavigationStackItemStateManagementScreenImpl _value,
      $Res Function(_$NavigationStackItemStateManagementScreenImpl) _then)
      : super(_value, _then);

  /// Create a copy of NavigationStackItem
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$NavigationStackItemStateManagementScreenImpl
    with DiagnosticableTreeMixin
    implements NavigationStackItemStateManagementScreen {
  const _$NavigationStackItemStateManagementScreenImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'NavigationStackItem.stateManagement()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'NavigationStackItem.stateManagement'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NavigationStackItemStateManagementScreenImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() splash,
    required TResult Function() login,
    required TResult Function() dashboard,
    required TResult Function() memberManagement,
    required TResult Function() eventManagement,
    required TResult Function() stateManagement,
    required TResult Function() cityManagement,
    required TResult Function() rulesManagement,
    required TResult Function() aboutManagement,
  }) {
    return stateManagement();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? splash,
    TResult? Function()? login,
    TResult? Function()? dashboard,
    TResult? Function()? memberManagement,
    TResult? Function()? eventManagement,
    TResult? Function()? stateManagement,
    TResult? Function()? cityManagement,
    TResult? Function()? rulesManagement,
    TResult? Function()? aboutManagement,
  }) {
    return stateManagement?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? splash,
    TResult Function()? login,
    TResult Function()? dashboard,
    TResult Function()? memberManagement,
    TResult Function()? eventManagement,
    TResult Function()? stateManagement,
    TResult Function()? cityManagement,
    TResult Function()? rulesManagement,
    TResult Function()? aboutManagement,
    required TResult orElse(),
  }) {
    if (stateManagement != null) {
      return stateManagement();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NavigationStackItemSplashPage value) splash,
    required TResult Function(NavigationStackItemLoginPage value) login,
    required TResult Function(NavigationStackItemDashboardScreen value)
        dashboard,
    required TResult Function(NavigationStackItemMemberManagementScreen value)
        memberManagement,
    required TResult Function(NavigationStackItemEventManagementScreen value)
        eventManagement,
    required TResult Function(NavigationStackItemStateManagementScreen value)
        stateManagement,
    required TResult Function(NavigationStackItemCityManagementScreen value)
        cityManagement,
    required TResult Function(NavigationStackItemRulesManagementScreen value)
        rulesManagement,
    required TResult Function(NavigationStackItemAboutManagementScreen value)
        aboutManagement,
  }) {
    return stateManagement(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NavigationStackItemSplashPage value)? splash,
    TResult? Function(NavigationStackItemLoginPage value)? login,
    TResult? Function(NavigationStackItemDashboardScreen value)? dashboard,
    TResult? Function(NavigationStackItemMemberManagementScreen value)?
        memberManagement,
    TResult? Function(NavigationStackItemEventManagementScreen value)?
        eventManagement,
    TResult? Function(NavigationStackItemStateManagementScreen value)?
        stateManagement,
    TResult? Function(NavigationStackItemCityManagementScreen value)?
        cityManagement,
    TResult? Function(NavigationStackItemRulesManagementScreen value)?
        rulesManagement,
    TResult? Function(NavigationStackItemAboutManagementScreen value)?
        aboutManagement,
  }) {
    return stateManagement?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NavigationStackItemSplashPage value)? splash,
    TResult Function(NavigationStackItemLoginPage value)? login,
    TResult Function(NavigationStackItemDashboardScreen value)? dashboard,
    TResult Function(NavigationStackItemMemberManagementScreen value)?
        memberManagement,
    TResult Function(NavigationStackItemEventManagementScreen value)?
        eventManagement,
    TResult Function(NavigationStackItemStateManagementScreen value)?
        stateManagement,
    TResult Function(NavigationStackItemCityManagementScreen value)?
        cityManagement,
    TResult Function(NavigationStackItemRulesManagementScreen value)?
        rulesManagement,
    TResult Function(NavigationStackItemAboutManagementScreen value)?
        aboutManagement,
    required TResult orElse(),
  }) {
    if (stateManagement != null) {
      return stateManagement(this);
    }
    return orElse();
  }
}

abstract class NavigationStackItemStateManagementScreen
    implements NavigationStackItem {
  const factory NavigationStackItemStateManagementScreen() =
      _$NavigationStackItemStateManagementScreenImpl;
}

/// @nodoc
abstract class _$$NavigationStackItemCityManagementScreenImplCopyWith<$Res> {
  factory _$$NavigationStackItemCityManagementScreenImplCopyWith(
          _$NavigationStackItemCityManagementScreenImpl value,
          $Res Function(_$NavigationStackItemCityManagementScreenImpl) then) =
      __$$NavigationStackItemCityManagementScreenImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$NavigationStackItemCityManagementScreenImplCopyWithImpl<$Res>
    extends _$NavigationStackItemCopyWithImpl<$Res,
        _$NavigationStackItemCityManagementScreenImpl>
    implements _$$NavigationStackItemCityManagementScreenImplCopyWith<$Res> {
  __$$NavigationStackItemCityManagementScreenImplCopyWithImpl(
      _$NavigationStackItemCityManagementScreenImpl _value,
      $Res Function(_$NavigationStackItemCityManagementScreenImpl) _then)
      : super(_value, _then);

  /// Create a copy of NavigationStackItem
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$NavigationStackItemCityManagementScreenImpl
    with DiagnosticableTreeMixin
    implements NavigationStackItemCityManagementScreen {
  const _$NavigationStackItemCityManagementScreenImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'NavigationStackItem.cityManagement()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'NavigationStackItem.cityManagement'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NavigationStackItemCityManagementScreenImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() splash,
    required TResult Function() login,
    required TResult Function() dashboard,
    required TResult Function() memberManagement,
    required TResult Function() eventManagement,
    required TResult Function() stateManagement,
    required TResult Function() cityManagement,
    required TResult Function() rulesManagement,
    required TResult Function() aboutManagement,
  }) {
    return cityManagement();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? splash,
    TResult? Function()? login,
    TResult? Function()? dashboard,
    TResult? Function()? memberManagement,
    TResult? Function()? eventManagement,
    TResult? Function()? stateManagement,
    TResult? Function()? cityManagement,
    TResult? Function()? rulesManagement,
    TResult? Function()? aboutManagement,
  }) {
    return cityManagement?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? splash,
    TResult Function()? login,
    TResult Function()? dashboard,
    TResult Function()? memberManagement,
    TResult Function()? eventManagement,
    TResult Function()? stateManagement,
    TResult Function()? cityManagement,
    TResult Function()? rulesManagement,
    TResult Function()? aboutManagement,
    required TResult orElse(),
  }) {
    if (cityManagement != null) {
      return cityManagement();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NavigationStackItemSplashPage value) splash,
    required TResult Function(NavigationStackItemLoginPage value) login,
    required TResult Function(NavigationStackItemDashboardScreen value)
        dashboard,
    required TResult Function(NavigationStackItemMemberManagementScreen value)
        memberManagement,
    required TResult Function(NavigationStackItemEventManagementScreen value)
        eventManagement,
    required TResult Function(NavigationStackItemStateManagementScreen value)
        stateManagement,
    required TResult Function(NavigationStackItemCityManagementScreen value)
        cityManagement,
    required TResult Function(NavigationStackItemRulesManagementScreen value)
        rulesManagement,
    required TResult Function(NavigationStackItemAboutManagementScreen value)
        aboutManagement,
  }) {
    return cityManagement(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NavigationStackItemSplashPage value)? splash,
    TResult? Function(NavigationStackItemLoginPage value)? login,
    TResult? Function(NavigationStackItemDashboardScreen value)? dashboard,
    TResult? Function(NavigationStackItemMemberManagementScreen value)?
        memberManagement,
    TResult? Function(NavigationStackItemEventManagementScreen value)?
        eventManagement,
    TResult? Function(NavigationStackItemStateManagementScreen value)?
        stateManagement,
    TResult? Function(NavigationStackItemCityManagementScreen value)?
        cityManagement,
    TResult? Function(NavigationStackItemRulesManagementScreen value)?
        rulesManagement,
    TResult? Function(NavigationStackItemAboutManagementScreen value)?
        aboutManagement,
  }) {
    return cityManagement?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NavigationStackItemSplashPage value)? splash,
    TResult Function(NavigationStackItemLoginPage value)? login,
    TResult Function(NavigationStackItemDashboardScreen value)? dashboard,
    TResult Function(NavigationStackItemMemberManagementScreen value)?
        memberManagement,
    TResult Function(NavigationStackItemEventManagementScreen value)?
        eventManagement,
    TResult Function(NavigationStackItemStateManagementScreen value)?
        stateManagement,
    TResult Function(NavigationStackItemCityManagementScreen value)?
        cityManagement,
    TResult Function(NavigationStackItemRulesManagementScreen value)?
        rulesManagement,
    TResult Function(NavigationStackItemAboutManagementScreen value)?
        aboutManagement,
    required TResult orElse(),
  }) {
    if (cityManagement != null) {
      return cityManagement(this);
    }
    return orElse();
  }
}

abstract class NavigationStackItemCityManagementScreen
    implements NavigationStackItem {
  const factory NavigationStackItemCityManagementScreen() =
      _$NavigationStackItemCityManagementScreenImpl;
}

/// @nodoc
abstract class _$$NavigationStackItemRulesManagementScreenImplCopyWith<$Res> {
  factory _$$NavigationStackItemRulesManagementScreenImplCopyWith(
          _$NavigationStackItemRulesManagementScreenImpl value,
          $Res Function(_$NavigationStackItemRulesManagementScreenImpl) then) =
      __$$NavigationStackItemRulesManagementScreenImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$NavigationStackItemRulesManagementScreenImplCopyWithImpl<$Res>
    extends _$NavigationStackItemCopyWithImpl<$Res,
        _$NavigationStackItemRulesManagementScreenImpl>
    implements _$$NavigationStackItemRulesManagementScreenImplCopyWith<$Res> {
  __$$NavigationStackItemRulesManagementScreenImplCopyWithImpl(
      _$NavigationStackItemRulesManagementScreenImpl _value,
      $Res Function(_$NavigationStackItemRulesManagementScreenImpl) _then)
      : super(_value, _then);

  /// Create a copy of NavigationStackItem
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$NavigationStackItemRulesManagementScreenImpl
    with DiagnosticableTreeMixin
    implements NavigationStackItemRulesManagementScreen {
  const _$NavigationStackItemRulesManagementScreenImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'NavigationStackItem.rulesManagement()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'NavigationStackItem.rulesManagement'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NavigationStackItemRulesManagementScreenImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() splash,
    required TResult Function() login,
    required TResult Function() dashboard,
    required TResult Function() memberManagement,
    required TResult Function() eventManagement,
    required TResult Function() stateManagement,
    required TResult Function() cityManagement,
    required TResult Function() rulesManagement,
    required TResult Function() aboutManagement,
  }) {
    return rulesManagement();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? splash,
    TResult? Function()? login,
    TResult? Function()? dashboard,
    TResult? Function()? memberManagement,
    TResult? Function()? eventManagement,
    TResult? Function()? stateManagement,
    TResult? Function()? cityManagement,
    TResult? Function()? rulesManagement,
    TResult? Function()? aboutManagement,
  }) {
    return rulesManagement?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? splash,
    TResult Function()? login,
    TResult Function()? dashboard,
    TResult Function()? memberManagement,
    TResult Function()? eventManagement,
    TResult Function()? stateManagement,
    TResult Function()? cityManagement,
    TResult Function()? rulesManagement,
    TResult Function()? aboutManagement,
    required TResult orElse(),
  }) {
    if (rulesManagement != null) {
      return rulesManagement();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NavigationStackItemSplashPage value) splash,
    required TResult Function(NavigationStackItemLoginPage value) login,
    required TResult Function(NavigationStackItemDashboardScreen value)
        dashboard,
    required TResult Function(NavigationStackItemMemberManagementScreen value)
        memberManagement,
    required TResult Function(NavigationStackItemEventManagementScreen value)
        eventManagement,
    required TResult Function(NavigationStackItemStateManagementScreen value)
        stateManagement,
    required TResult Function(NavigationStackItemCityManagementScreen value)
        cityManagement,
    required TResult Function(NavigationStackItemRulesManagementScreen value)
        rulesManagement,
    required TResult Function(NavigationStackItemAboutManagementScreen value)
        aboutManagement,
  }) {
    return rulesManagement(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NavigationStackItemSplashPage value)? splash,
    TResult? Function(NavigationStackItemLoginPage value)? login,
    TResult? Function(NavigationStackItemDashboardScreen value)? dashboard,
    TResult? Function(NavigationStackItemMemberManagementScreen value)?
        memberManagement,
    TResult? Function(NavigationStackItemEventManagementScreen value)?
        eventManagement,
    TResult? Function(NavigationStackItemStateManagementScreen value)?
        stateManagement,
    TResult? Function(NavigationStackItemCityManagementScreen value)?
        cityManagement,
    TResult? Function(NavigationStackItemRulesManagementScreen value)?
        rulesManagement,
    TResult? Function(NavigationStackItemAboutManagementScreen value)?
        aboutManagement,
  }) {
    return rulesManagement?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NavigationStackItemSplashPage value)? splash,
    TResult Function(NavigationStackItemLoginPage value)? login,
    TResult Function(NavigationStackItemDashboardScreen value)? dashboard,
    TResult Function(NavigationStackItemMemberManagementScreen value)?
        memberManagement,
    TResult Function(NavigationStackItemEventManagementScreen value)?
        eventManagement,
    TResult Function(NavigationStackItemStateManagementScreen value)?
        stateManagement,
    TResult Function(NavigationStackItemCityManagementScreen value)?
        cityManagement,
    TResult Function(NavigationStackItemRulesManagementScreen value)?
        rulesManagement,
    TResult Function(NavigationStackItemAboutManagementScreen value)?
        aboutManagement,
    required TResult orElse(),
  }) {
    if (rulesManagement != null) {
      return rulesManagement(this);
    }
    return orElse();
  }
}

abstract class NavigationStackItemRulesManagementScreen
    implements NavigationStackItem {
  const factory NavigationStackItemRulesManagementScreen() =
      _$NavigationStackItemRulesManagementScreenImpl;
}

/// @nodoc
abstract class _$$NavigationStackItemAboutManagementScreenImplCopyWith<$Res> {
  factory _$$NavigationStackItemAboutManagementScreenImplCopyWith(
          _$NavigationStackItemAboutManagementScreenImpl value,
          $Res Function(_$NavigationStackItemAboutManagementScreenImpl) then) =
      __$$NavigationStackItemAboutManagementScreenImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$NavigationStackItemAboutManagementScreenImplCopyWithImpl<$Res>
    extends _$NavigationStackItemCopyWithImpl<$Res,
        _$NavigationStackItemAboutManagementScreenImpl>
    implements _$$NavigationStackItemAboutManagementScreenImplCopyWith<$Res> {
  __$$NavigationStackItemAboutManagementScreenImplCopyWithImpl(
      _$NavigationStackItemAboutManagementScreenImpl _value,
      $Res Function(_$NavigationStackItemAboutManagementScreenImpl) _then)
      : super(_value, _then);

  /// Create a copy of NavigationStackItem
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$NavigationStackItemAboutManagementScreenImpl
    with DiagnosticableTreeMixin
    implements NavigationStackItemAboutManagementScreen {
  const _$NavigationStackItemAboutManagementScreenImpl();

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return 'NavigationStackItem.aboutManagement()';
  }

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties
      ..add(DiagnosticsProperty('type', 'NavigationStackItem.aboutManagement'));
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NavigationStackItemAboutManagementScreenImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() splash,
    required TResult Function() login,
    required TResult Function() dashboard,
    required TResult Function() memberManagement,
    required TResult Function() eventManagement,
    required TResult Function() stateManagement,
    required TResult Function() cityManagement,
    required TResult Function() rulesManagement,
    required TResult Function() aboutManagement,
  }) {
    return aboutManagement();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? splash,
    TResult? Function()? login,
    TResult? Function()? dashboard,
    TResult? Function()? memberManagement,
    TResult? Function()? eventManagement,
    TResult? Function()? stateManagement,
    TResult? Function()? cityManagement,
    TResult? Function()? rulesManagement,
    TResult? Function()? aboutManagement,
  }) {
    return aboutManagement?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? splash,
    TResult Function()? login,
    TResult Function()? dashboard,
    TResult Function()? memberManagement,
    TResult Function()? eventManagement,
    TResult Function()? stateManagement,
    TResult Function()? cityManagement,
    TResult Function()? rulesManagement,
    TResult Function()? aboutManagement,
    required TResult orElse(),
  }) {
    if (aboutManagement != null) {
      return aboutManagement();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(NavigationStackItemSplashPage value) splash,
    required TResult Function(NavigationStackItemLoginPage value) login,
    required TResult Function(NavigationStackItemDashboardScreen value)
        dashboard,
    required TResult Function(NavigationStackItemMemberManagementScreen value)
        memberManagement,
    required TResult Function(NavigationStackItemEventManagementScreen value)
        eventManagement,
    required TResult Function(NavigationStackItemStateManagementScreen value)
        stateManagement,
    required TResult Function(NavigationStackItemCityManagementScreen value)
        cityManagement,
    required TResult Function(NavigationStackItemRulesManagementScreen value)
        rulesManagement,
    required TResult Function(NavigationStackItemAboutManagementScreen value)
        aboutManagement,
  }) {
    return aboutManagement(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(NavigationStackItemSplashPage value)? splash,
    TResult? Function(NavigationStackItemLoginPage value)? login,
    TResult? Function(NavigationStackItemDashboardScreen value)? dashboard,
    TResult? Function(NavigationStackItemMemberManagementScreen value)?
        memberManagement,
    TResult? Function(NavigationStackItemEventManagementScreen value)?
        eventManagement,
    TResult? Function(NavigationStackItemStateManagementScreen value)?
        stateManagement,
    TResult? Function(NavigationStackItemCityManagementScreen value)?
        cityManagement,
    TResult? Function(NavigationStackItemRulesManagementScreen value)?
        rulesManagement,
    TResult? Function(NavigationStackItemAboutManagementScreen value)?
        aboutManagement,
  }) {
    return aboutManagement?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(NavigationStackItemSplashPage value)? splash,
    TResult Function(NavigationStackItemLoginPage value)? login,
    TResult Function(NavigationStackItemDashboardScreen value)? dashboard,
    TResult Function(NavigationStackItemMemberManagementScreen value)?
        memberManagement,
    TResult Function(NavigationStackItemEventManagementScreen value)?
        eventManagement,
    TResult Function(NavigationStackItemStateManagementScreen value)?
        stateManagement,
    TResult Function(NavigationStackItemCityManagementScreen value)?
        cityManagement,
    TResult Function(NavigationStackItemRulesManagementScreen value)?
        rulesManagement,
    TResult Function(NavigationStackItemAboutManagementScreen value)?
        aboutManagement,
    required TResult orElse(),
  }) {
    if (aboutManagement != null) {
      return aboutManagement(this);
    }
    return orElse();
  }
}

abstract class NavigationStackItemAboutManagementScreen
    implements NavigationStackItem {
  const factory NavigationStackItemAboutManagementScreen() =
      _$NavigationStackItemAboutManagementScreenImpl;
}
