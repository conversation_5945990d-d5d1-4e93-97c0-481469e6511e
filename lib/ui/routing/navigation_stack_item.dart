import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter/foundation.dart';
part 'navigation_stack_item.freezed.dart';

@freezed
class NavigationStackItem with _$NavigationStackItem {
  const factory NavigationStackItem.splash() = NavigationStackItemSplashPage;
  const factory NavigationStackItem.login() = NavigationStackItemLoginPage;
  const factory NavigationStackItem.dashboard() = NavigationStackItemDashboardScreen;
  const factory NavigationStackItem.memberManagement() = NavigationStackItemMemberManagementScreen;
  const factory NavigationStackItem.eventManagement() = NavigationStackItemEventManagementScreen;
  const factory NavigationStackItem.stateManagement() = NavigationStackItemStateManagementScreen;
  const factory NavigationStackItem.cityManagement() = NavigationStackItemCityManagementScreen;
  const factory NavigationStackItem.rulesManagement() = NavigationStackItemRulesManagementScreen;
  const factory NavigationStackItem.aboutManagement() = NavigationStackItemAboutManagementScreen;
}