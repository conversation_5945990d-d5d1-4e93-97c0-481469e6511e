import 'package:ds_admin/ui/routing/navigation_stack_item.dart';
import 'package:ds_admin/ui/routing/navigation_stack_keys.dart';
import 'package:ds_admin/ui/routing/stack.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:injectable/injectable.dart';

@injectable
class MainRouterInformationParser
    extends RouteInformationParser<NavigationStack> {
  WidgetRef ref;
  BuildContext context;

  MainRouterInformationParser(
      @factoryParam this.ref, @factoryParam this.context);

  @override
  Future<NavigationStack> parseRouteInformation(
      RouteInformation routeInformation) async {
    debugPrint('........URL......${routeInformation.uri}');

    final Uri uri = routeInformation.uri;
    // final queryParams = uri.queryParameters;
    final items = <NavigationStackItem>[];
    debugPrint('Stack Item Count - ${items.length}');
    // String? langStr = queryParams['lang'];

    // if (langStr != null) {
    //   await context.setLocale(Locale(langStr == "english" ? "en" : "hi"));
    // }

    for (var i = 0; i < uri.pathSegments.length; i = i + 1) {
      final key = uri.pathSegments[i];

      switch (key) {
        case Keys.splash:
          items.add(const NavigationStackItem.splash());
          break;
        case Keys.login:
          items.add(const NavigationStackItem.login());
          break;
        case Keys.dashboard:
          items.add(const NavigationStackItem.dashboard());
          break;
        case Keys.memberManagement:
          items.add(const NavigationStackItem.memberManagement());
          break;
        case Keys.eventManagement:
          items.add(const NavigationStackItem.eventManagement());
          break;
        case Keys.stateManagement:
          items.add(const NavigationStackItem.stateManagement());
          break;
        case Keys.cityManagement:
          items.add(const NavigationStackItem.cityManagement());
          break;
        case Keys.rulesManagement:
          items.add(const NavigationStackItem.rulesManagement());
          break;
        case Keys.aboutManagement:
          items.add(const NavigationStackItem.aboutManagement());
          break;

        default:
          items.add(const NavigationStackItem.splash());
        // default:
        //   items.add(const NavigationStackItem.notFound());
      }
    }
    if (items.isEmpty) {
      const fallback = NavigationStackItem.splash();
      if (items.isNotEmpty && items.first is NavigationStackItemSplashPage) {
        items[0] = fallback;
      } else {
        items.insert(0, fallback);
      }
    }
    return NavigationStack(items);
  }

  ///THIS IS IMPORTANT: Here we restore the web history
  @override
  RouteInformation restoreRouteInformation(NavigationStack configuration) {
    final location = configuration.items.fold<String>('', (previousValue, element) {
      return previousValue +
          element.when(
            splash: () => '/${Keys.splash}',
            login: () => '/${Keys.login}',
            dashboard: () => '/${Keys.dashboard}',
            memberManagement: () => '/${Keys.memberManagement}',
            eventManagement: () => '/${Keys.eventManagement}',
            stateManagement: () => '/${Keys.stateManagement}',
            cityManagement: () => '/${Keys.cityManagement}',
            rulesManagement: () => '/${Keys.rulesManagement}',
            aboutManagement: () => '/${Keys.aboutManagement}',
          );
    });
    return RouteInformation(uri: Uri.parse(location));
  }
}
