import 'package:ds_admin/framework/utils/extension/extension.dart';
import 'package:ds_admin/framework/utils/extension/string_extension.dart';
import 'package:ds_admin/ui/utils/theme/locale_keys.g.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../framework/controller/login/login_controller.dart';
import '../../utils/theme/theme.dart';
import '../../utils/widgets/dialog_progressbar.dart';
import '../helper/login_form_widget.dart';

class LoginMobile extends ConsumerStatefulWidget {
  const LoginMobile({super.key});

  @override
  ConsumerState<LoginMobile> createState() => _LoginMobileState();
}

class _LoginMobileState extends ConsumerState<LoginMobile> {

  @override
  Widget build(BuildContext context) {
    final loginWatch = ref.watch(loginController);
    return Scaffold(
      backgroundColor: AppColors.primary,
      body: SafeArea(
        child: Stack(
          children: [
            SingleChildScrollView(
              child: bodyWidget(),
            ),
            DialogProgressBar(isLoading: loginWatch.isLoading)
          ],
        ),
      ),
    );
  }

  ///Body Widget
  Widget bodyWidget() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          padding: EdgeInsets.only(left: 20.w, right: 20.w, top: 20.h),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                LocaleKeys.keyWelcome.localized,
                style: TextStyles.bold.copyWith(color: AppColors.white, fontSize: 35.sp),
              ).paddingOnly(bottom: 25.h, top: 64.h),
              Text(
                LocaleKeys.keyLoginToAdminPanel.localized,
                style: TextStyles.bold.copyWith(color: AppColors.white, fontSize: 25.sp),
              ).paddingOnly(bottom: 25.h,),
              LoginFormWidget(isShowLogo: true),
            ],
          ),
        ),
      ],
    );
  }
}
