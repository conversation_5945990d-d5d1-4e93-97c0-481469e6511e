import 'package:ds_admin/ui/login/mobile/login_mobile.dart';
import 'package:ds_admin/ui/login/web/login_web.dart';
import 'package:flutter/material.dart';
import 'package:responsive_builder/responsive_builder.dart';

class Login extends StatelessWidget {
  const Login({super.key});

  ///Build Override
  @override
  Widget build(BuildContext context) {
    return ScreenTypeLayout.builder(
      mobile: (BuildContext context) {
        return const LoginMobile();
      },
      desktop: (BuildContext context) {
        return const LoginWeb();
      },
    );
  }
}
