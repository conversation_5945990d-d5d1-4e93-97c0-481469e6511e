import 'package:ds_admin/framework/controller/login/login_controller.dart';
import 'package:ds_admin/framework/utils/extension/extension.dart';
import 'package:ds_admin/framework/utils/extension/string_extension.dart';
import 'package:ds_admin/ui/utils/theme/locale_keys.g.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../utils/const/form_validations.dart';
import '../../utils/theme/theme.dart';
import '../../utils/widgets/common_button.dart';
import '../../utils/widgets/common_form_field.dart';

class LoginFormWidget extends StatefulWidget {
  const LoginFormWidget({
    super.key,
    required this.isShowLogo,
  });

  final bool isShowLogo;

  @override
  State<LoginFormWidget> createState() => _LoginFormWidgetState();
}

class _LoginFormWidgetState extends State<LoginFormWidget> {

  TextEditingController txtUsernameCTR = TextEditingController();
  TextEditingController txtPasswordCTR = TextEditingController();

  /// Form Key
  final _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 15.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.all(Radius.circular(10.r)),
        boxShadow: const [
          BoxShadow(
            color: AppColors.black, // shadow color
            blurRadius: 10, // shadow radius
            offset: Offset(0, 1), // shadow offset
            spreadRadius: 0.1, // The amount the box should be inflated prior to applying the blur
            blurStyle: BlurStyle.normal,
          ),
        ],
      ),
      child: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [

            widget.isShowLogo ? Image.asset(AppAssets.dsLogo, height: 120.h, width: 120.h,).alignAtCenter().paddingSymmetric(vertical: 20.h): SizedBox(height: 20.h,),

            /// Username
            Text(
              LocaleKeys.keyUsername.localized,
              style: TextStyles.medium.copyWith(color: AppColors.black),
            ).paddingOnly(bottom: 10.h),
            CommonInputFormField(
              textEditingController: txtUsernameCTR,
              textInputAction: TextInputAction.next,
              textInputType: TextInputType.text,
              textInputFormatter: [
                LengthLimitingTextInputFormatter(10),
              ],
              validator: (str) {
                return validateEmpty(str, LocaleKeys.keyEnterUsername.localized);
              },
            ).paddingOnly(bottom: 20.h),

            /// Password
            Text(
              LocaleKeys.keyPassword.localized,
              style: TextStyles.medium.copyWith(color: AppColors.black),
            ).paddingOnly(bottom: 10.h),
            CommonInputFormField(
              obscureText: true,
              textEditingController: txtPasswordCTR,
              textInputAction: TextInputAction.done,
              textInputType: TextInputType.text,
              validator: (str) {
                return validateEmpty(str, LocaleKeys.keyEnterPassword.localized);
              },
            ).paddingOnly(bottom: 20.h),

            Consumer(
              builder: (BuildContext context, WidgetRef ref, Widget? child) {
                final loginWatch = ref.read(loginController);
                return CommonButton(
                  showLoader: false,
                  buttonText: LocaleKeys.keyLogin.localized.toUpperCase(),
                  onTap: () async {
                    final value = _formKey.currentState?.validate();
                    if (value == true) {
                      // await loginApiCall();
                      loginWatch.loginApiCall(context, ref, txtUsernameCTR.text.toString(), txtPasswordCTR.text.toString());
                      // ref.read(navigationStackController).push(const NavigationStackItem.shoppingList());
                    }
                  },
                );
              },
            ),

            SizedBox(
              height: 20.h,
            )
          ],
        ),
      ),
    );
  }
}