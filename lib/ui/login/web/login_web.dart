import 'package:ds_admin/framework/utils/extension/extension.dart';
import 'package:ds_admin/framework/utils/extension/string_extension.dart';
import 'package:ds_admin/ui/login/helper/login_form_widget.dart';
import 'package:ds_admin/ui/utils/widgets/dialog_progressbar.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../framework/controller/login/login_controller.dart';
import '../../utils/theme/locale_keys.g.dart';
import '../../utils/theme/theme.dart';

class LoginWeb extends ConsumerStatefulWidget {
  const LoginWeb({super.key});

  @override
  ConsumerState<LoginWeb> createState() => _LoginWebState();
}

class _LoginWebState extends ConsumerState<LoginWeb> {
  TextEditingController txtUsernameCTR = TextEditingController();
  TextEditingController txtPasswordCTR = TextEditingController();

  /// Form Key
  final _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    final loginWatch = ref.watch(loginController);
    return Scaffold(
      backgroundColor: AppColors.primary,
      body: Row(
        mainAxisSize: MainAxisSize.max,
        children: [
          Expanded(
            flex: 1,
            child: Container(
              color: AppColors.white,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Image.asset(AppAssets.dsLogo, height: 320.h, width: 320.h,).alignAtCenter().paddingSymmetric(vertical: 20.h),
                  Text(
                    LocaleKeys.keyWelcome.localized,
                    style: TextStyles.bold.copyWith(color: AppColors.primary, fontSize: 35.sp),
                  ).paddingOnly(bottom: 25.h),
                  Text(
                    LocaleKeys.keyLoginToAdminPanel.localized,
                    style: TextStyles.bold.copyWith(color: AppColors.primary, fontSize: 25.sp),
                  ).paddingOnly(bottom: 25.h,),
                ],
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Stack(
              children: [
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    LoginFormWidget(isShowLogo: false).paddingSymmetric(horizontal: 100.w),
                  ],
                ),
                DialogProgressBar(isLoading: loginWatch.isLoading)
              ],
            ),
          )
        ],
      ),
    );
  }
}
