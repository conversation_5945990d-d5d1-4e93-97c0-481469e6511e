import 'package:data_table_2/data_table_2.dart';
import 'package:ds_admin/framework/controller/member_management/member_management_controller.dart';
import 'package:ds_admin/framework/utils/extension/extension.dart';
import 'package:ds_admin/framework/utils/extension/string_extension.dart';
import 'package:ds_admin/framework/utils/session.dart';
import 'package:ds_admin/ui/utils/theme/locale_keys.g.dart';
import 'package:ds_admin/ui/utils/widgets/common_web_header.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../framework/repository/member_management/model/get_members_response.dart';
import '../../routing/navigation_stack_item.dart';
import '../../utils/theme/theme.dart';
import '../../utils/widgets/base_scaffold.dart';
import '../../utils/widgets/common_text.dart';
import '../../utils/widgets/dialog_progressbar.dart';
import '../helper/add_edit_member_dialog.dart';
import '../helper/filter_dialog.dart';

class MemberManagementWeb extends ConsumerStatefulWidget {
  const MemberManagementWeb({super.key});

  @override
  ConsumerState<MemberManagementWeb> createState() => _MemberManagementWebState();
}

class _MemberManagementWebState extends ConsumerState<MemberManagementWeb> {
  @override
  void initState() {
    super.initState();
    SchedulerBinding.instance.addPostFrameCallback((timeStamp) {
      ref.read(memberManagementController).getMembersListApiCall(context, ref, 1, Session.getAppLanguage());
    });
  }

  @override
  Widget build(BuildContext context) {
    var controller = ref.watch(memberManagementController);
    return BaseScaffold(
      route: NavigationStackItem.memberManagement(),
      isMobile: false,
      body: Stack(
        children: [
          Container(
            color: AppColors.white,
            height: MediaQuery.of(context).size.height,
            width: MediaQuery.of(context).size.width,
            child: Column(
              children: [
                CommonWebHeader(ref: ref),
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        Row(
                          children: [
                            Expanded(
                              flex: 3,
                              child: Card(
                                color: AppColors.greyF6F6F6,
                                child: Padding(
                                  padding: const EdgeInsets.all(16.0),
                                  child: Row(
                                    children: [
                                      const Icon(Icons.people),
                                      const SizedBox(width: 8),
                                      Text(
                                        '${LocaleKeys.keyTotalMembers.localized}: ${controller.membersInfo?.totalMembers}',
                                        style: const TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              flex: 3,
                              child: Card(
                                color: AppColors.greyF6F6F6,
                                child: Padding(
                                  padding: const EdgeInsets.all(16.0),
                                  child: Row(
                                    children: [
                                      const Icon(Icons.check_circle, color: Colors.green),
                                      const SizedBox(width: 8),
                                      Text(
                                        '${LocaleKeys.keyActiveMembers.localized}: ${controller.membersInfo?.activeMembers}',
                                        style: const TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              flex: 3,
                              child: Card(
                                color: AppColors.greyF6F6F6,
                                child: Padding(
                                  padding: const EdgeInsets.all(16.0),
                                  child: Row(
                                    children: [
                                      const Icon(Icons.cancel, color: Colors.red),
                                      const SizedBox(width: 8),
                                      Text(
                                        '${LocaleKeys.keyNaatbahar.localized}: ${controller.membersInfo?.naatbaharMembers}',
                                        style: const TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        Card(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Text(
                                    LocaleKeys.keyMembersList.localized,
                                    style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const Spacer(),
                                  ElevatedButton.icon(
                                    icon: Icon(
                                      Icons.add,
                                      color: AppColors.primary,
                                    ),
                                    label: CommonText(
                                      title: LocaleKeys.keyAddMember.localized,
                                      textStyle: TextStyles.regular.copyWith(color: AppColors.primary),
                                    ),
                                    onPressed: () {
                                      showDialog(
                                        context: context,
                                        barrierDismissible: false,
                                        builder: (context) => AddEditMemberDialog(),
                                      );
                                    },
                                    style: ButtonStyle(
                                      backgroundColor: WidgetStateProperty.all(AppColors.greyF6F6F6),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 16),
                              Row(
                                children: [
                                  Expanded(
                                    flex: 4,
                                    child: TextField(
                                      controller: controller.ctrlSearchMember,
                                      decoration: InputDecoration(
                                        hintText: LocaleKeys.keySearchByName.localized,
                                        prefixIcon: Icon(Icons.search),
                                        border: OutlineInputBorder(),
                                        enabledBorder: OutlineInputBorder(),
                                      ),
                                      onChanged: (value) {
                                        if (value.length > 3) {
                                          controller.getMembersListApiCall(
                                            context,
                                            ref,
                                            1,
                                            Session.getAppLanguage(),
                                            memberName: value,
                                            cityId: '${controller.selectedCity ?? ''}',
                                            minAge: controller.filterMinAge,
                                            maxAge: controller.filterMaxAge,
                                            martialStatus: controller.selectedMartialStatus ?? '',
                                          );
                                        } else if (value.isEmpty) {
                                          controller.getMembersListApiCall(
                                            context,
                                            ref,
                                            1,
                                            Session.getAppLanguage(),
                                            memberName: value,
                                            cityId: '${controller.selectedCity ?? ''}',
                                            minAge: controller.filterMinAge,
                                            maxAge: controller.filterMaxAge,
                                            martialStatus: controller.selectedMartialStatus ?? '',
                                          );
                                        }
                                      },
                                    ),
                                  ),
                                  const Spacer(),
                                  ElevatedButton.icon(
                                    icon: Icon(
                                      Icons.filter_alt,
                                      color: AppColors.primary,
                                    ),
                                    label: CommonText(
                                      title: LocaleKeys.keyApplyFilter.localized,
                                      textStyle: TextStyles.regular.copyWith(color: AppColors.primary),
                                    ),
                                    onPressed: () {
                                      showDialog(
                                        context: context,
                                        builder: (context) => FilterDialog(
                                          onApply: (int? cityId, int? minAge, int? maxAge, String? martialStatus) {
                                            controller.getMembersListApiCall(
                                              context,
                                              ref,
                                              1,
                                              Session.getAppLanguage(),
                                              cityId: (cityId ?? '').toString(),
                                              minAge: minAge,
                                              maxAge: maxAge,
                                              martialStatus: martialStatus ?? '',
                                            );
                                          },
                                        ),
                                      );
                                    },
                                    style: ButtonStyle(
                                      backgroundColor: WidgetStateProperty.all(controller.isFilterApplied ? AppColors.ascent : AppColors.greyF6F6F6),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 16),
                              (controller.members).isNotEmpty
                                  ? SizedBox(
                                      height: 400,
                                      child: DataTable2(
                                        columnSpacing: 12,
                                        dataRowHeight: 60,
                                        headingRowColor: WidgetStateProperty.all(Colors.grey.shade100),
                                        border: TableBorder.all(color: Colors.grey.shade300),
                                        columns: [
                                          DataColumn2(
                                            label: Text(LocaleKeys.keyName.localized),
                                            size: ColumnSize.L,
                                          ),
                                          DataColumn2(
                                            label: Text(LocaleKeys.keyMobile.localized),
                                            size: ColumnSize.M,
                                          ),
                                          DataColumn2(
                                            label: Text(LocaleKeys.keyAge.localized),
                                            size: ColumnSize.S,
                                          ),
                                          DataColumn2(
                                            label: Text(LocaleKeys.keyCity.localized),
                                            size: ColumnSize.S,
                                          ),
                                          DataColumn2(
                                            label: Text(LocaleKeys.keyAddress.localized),
                                            size: ColumnSize.M,
                                          ),
                                          DataColumn2(
                                            label: Text(LocaleKeys.keyStatus.localized),
                                            size: ColumnSize.S,
                                          ),
                                          DataColumn2(
                                            label: Text(LocaleKeys.keyActions.localized),
                                            size: ColumnSize.S,
                                          ),
                                        ],
                                        rows: List.generate(
                                            controller.members.length,
                                            (index) => DataRow(
                                                  cells: [
                                                    DataCell(Text(Session.getAppLanguage() == 'gu'
                                                        ? controller.members[index].memberNameGj ?? ''
                                                        : Session.getAppLanguage() == 'hi'
                                                            ? controller.members[index].memberNameHi ?? ''
                                                            : controller.members[index].memberName ?? '')),
                                                    DataCell(Text(controller.members[index].mobileNumber ?? '')),
                                                    DataCell(Text(controller.members[index].age.toString())),
                                                    DataCell(Text(controller.members[index].city ?? '')),
                                                    DataCell(Text(controller.members[index].address ?? '')),
                                                    DataCell(
                                                      Container(
                                                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                                        decoration: BoxDecoration(
                                                          color: (controller.members[index].member_status ?? 'Inactive') == 'Active' ? Colors.green.shade100 : Colors.red.shade100,
                                                          borderRadius: BorderRadius.circular(12),
                                                        ),
                                                        child: Text(
                                                          controller.members[index].member_status ?? 'Inactive',
                                                          style: TextStyle(
                                                            color: (controller.members[index].member_status ?? 'Inactive') == 'Active' ? Colors.green.shade800 : Colors.red.shade800,
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                    DataCell(
                                                      Row(
                                                        children: [
                                                          IconButton(
                                                            icon: Icon(Icons.edit, color: AppColors.primary),
                                                            onPressed: () {
                                                              controller.updateMemberFather(Member(memberId: controller.members[index].fatherId, memberName: controller.members[index].fatherName));
                                                              controller.updateMemberMother(Member(memberId: controller.members[index].motherId, memberName: controller.members[index].motherName));
                                                              controller.updateMemberSpouse(Member(memberId: controller.members[index].spouseId, memberName: controller.members[index].spouseName));

                                                              showDialog(
                                                                context: context,
                                                                barrierDismissible: false,
                                                                builder: (context) => AddEditMemberDialog(member: controller.members[index],),
                                                              );
                                                            },
                                                            tooltip: 'Edit',
                                                          ),
                                                          IconButton(
                                                            icon: Icon(Icons.delete, color: AppColors.red),
                                                            onPressed: () {
                                                              showDialog(
                                                                context: context,
                                                                builder: (BuildContext context) {
                                                                  return AlertDialog(
                                                                    title: Text(LocaleKeys.keyConfirmDeleteMsg.localized),
                                                                    actions: [
                                                                      TextButton(
                                                                        child: Text(LocaleKeys.keyCancel.localized, style: TextStyle(color: AppColors.primary)),
                                                                        onPressed: () {
                                                                          Navigator.of(context).pop();
                                                                        },
                                                                      ),
                                                                      TextButton(
                                                                        child: Text(LocaleKeys.keyDelete.localized, style: TextStyle(color: AppColors.red)),
                                                                        onPressed: () {
                                                                          if(controller.members[index].memberId != null){
                                                                            controller.deleteMemberApiCall(context, ref, controller.members[index].memberId!);
                                                                          }
                                                                          Navigator.of(context).pop();
                                                                        },
                                                                      ),
                                                                    ],
                                                                  );
                                                                },
                                                              );
                                                            },
                                                            tooltip: 'Delete',
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  ],
                                                )),
                                      ),
                                    )
                                  : SizedBox(
                                      height: 400,
                                      child: _buildEmptyState(),
                                    ),
                            ],
                          ).paddingSymmetric(horizontal: 16, vertical: 16),
                        )
                      ],
                    ).paddingSymmetric(horizontal: 20, vertical: 20),
                  ),
                ),
              ],
            ),
          ),
          DialogProgressBar(isLoading: controller.isLoading),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.people_outline,
            size: 80,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'No members found',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade700,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Add a new member to get started',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }
}
