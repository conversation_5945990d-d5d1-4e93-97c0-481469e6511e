import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:ds_admin/framework/controller/member_management/member_management_controller.dart';
import 'package:ds_admin/framework/utils/extension/extension.dart';
import 'package:ds_admin/framework/utils/extension/string_extension.dart';
import 'package:ds_admin/ui/utils/theme/locale_keys.g.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../utils/theme/theme.dart';

class FilterDialog extends ConsumerStatefulWidget {
  final Function(int? cityId, int? minAge, int? maxAge, String? martialStatus) onApply;

  const FilterDialog({super.key, required this.onApply});

  @override
  ConsumerState<FilterDialog> createState() => _FilterDialogState();
}

class _FilterDialogState extends ConsumerState<FilterDialog> {

  @override
  void initState() {
    super.initState();
    ref.read(memberManagementController).getCities(null);
  }

  @override
  void dispose() {
    super.dispose();
  }


  @override
  Widget build(BuildContext context) {
    final memberController = ref.watch(memberManagementController);
    return AlertDialog(
      title: Text(LocaleKeys.keyApplyFilter.localized),
      content: SizedBox(
        width: 400,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // City Dropdown
            Consumer(
              builder: (BuildContext context, WidgetRef ref, Widget? child) {
                var controller = ref.watch(memberManagementController);
                return DropdownButton2<int>(
                  isExpanded: false,
                  underline: Container(),
                  hint: Text(LocaleKeys.keySelectCity.localized),
                  value: controller.selectedCity,
                  items: memberController.cities.map((city) {
                    return DropdownMenuItem<int>(
                      value: city.cityId,
                      child: Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: AppColors.greyF6F6F6,
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              city.cityCode ?? '',
                              style: TextStyle(
                                color: Colors.blue[800],
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(city.cityName ?? ''),
                        ],
                      ),
                    );
                  }).toList(),
                  onChanged: (int? value) {
                    if (value != null) {
                      controller.updateSelectedCity(value);
                    }
                  },
                  buttonStyleData: ButtonStyleData(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    height: 50,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(4),
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                  ),
                  dropdownStyleData: DropdownStyleData(
                    maxHeight: 200,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(4),
                        color: AppColors.white
                    ),

                  ),
                  menuItemStyleData: MenuItemStyleData(
                    height: 40,
                    overlayColor: WidgetStateProperty.all(AppColors.white),
                  ),
                );
              },
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: memberController.minAgeController,
                    decoration: InputDecoration(
                      labelText: LocaleKeys.keyMinimumAge.localized,
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.digitsOnly,
                      LengthLimitingTextInputFormatter(3),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextField(
                    controller: memberController.maxAgeController,
                    decoration: InputDecoration(
                      labelText: LocaleKeys.keyMaximumAge.localized,
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.digitsOnly,
                      LengthLimitingTextInputFormatter(3),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // Martial Status Dropdown
            Consumer(
              builder: (BuildContext context, WidgetRef ref, Widget? child) {
                var controller = ref.watch(memberManagementController);
                return DropdownButton2<String>(
                  isExpanded: false,
                  underline: Container(),
                  hint: Text(LocaleKeys.keyMartialStatus.localized),
                  value: controller.selectedMartialStatus,
                  items: memberController.martialStatusList.map((status) {
                    return DropdownMenuItem<String>(
                      value: status,
                      child: Row(
                        children: [
                          Text(status),
                        ],
                      ),
                    );
                  }).toList(),
                  onChanged: (String? value) {
                    if (value != null) {
                      controller.updateSelectedMartialStatus(value);
                    }
                  },
                  buttonStyleData: ButtonStyleData(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    height: 50,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(4),
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                  ),
                  dropdownStyleData: DropdownStyleData(
                    maxHeight: 200,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(4),
                        color: AppColors.white
                    ),

                  ),
                  menuItemStyleData: MenuItemStyleData(
                    height: 40,
                    overlayColor: WidgetStateProperty.all(AppColors.white),
                  ),
                );
              },
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text(LocaleKeys.keyCancel.localized, style: TextStyles.regular.copyWith(color: AppColors.red),),
        ).paddingSymmetric(horizontal: 5,vertical: 1),
        ElevatedButton(
          onPressed: () {
            memberController.filterMinAge = null;
            memberController.filterMaxAge = null;
            memberController.maxAgeController.text = '';
            memberController.minAgeController.text = '';
            memberController.selectedCity = null;
            memberController.selectedMartialStatus = null;
            memberController.updateFilterApplied(false);

            widget.onApply(
              memberController.selectedCity,
              memberController.filterMinAge,
              memberController.filterMaxAge,
              memberController.selectedMartialStatus,
            );
            Navigator.pop(context);
          },
          style: ButtonStyle(
            backgroundColor: WidgetStateProperty.all(AppColors.greyF6F6F6),
          ),
          child: Text(LocaleKeys.keyResetFilter.localized, style: TextStyles.regular.copyWith(color: AppColors.primary),),
        ).paddingSymmetric(horizontal: 5,vertical: 5),
        ElevatedButton(
          onPressed: () {
            if(memberController.minAgeController.text.isEmpty && memberController.maxAgeController.text.isEmpty && memberController.selectedCity == null && memberController.selectedMartialStatus == null){
              return;
            }
            memberController.filterMinAge = memberController.minAgeController.text.isEmpty ? null : int.parse(memberController.minAgeController.text);
            memberController.filterMaxAge = memberController.maxAgeController.text.isEmpty ? null : int.parse(memberController.maxAgeController.text);

            widget.onApply(
              memberController.selectedCity,
              memberController.filterMinAge,
              memberController.filterMaxAge,
              memberController.selectedMartialStatus,
            );
            memberController.updateFilterApplied(true);
            Navigator.pop(context);
          },
          style: ButtonStyle(
            backgroundColor: WidgetStateProperty.all(AppColors.greyF6F6F6),
          ),
          child: Text(LocaleKeys.keyApplyFilter.localized,style: TextStyles.regular.copyWith(color: AppColors.primary)),
        ).paddingSymmetric(horizontal: 5,vertical: 5),
      ],
    );
  }
}