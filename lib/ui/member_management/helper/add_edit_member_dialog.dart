import 'package:ds_admin/framework/controller/member_management/member_management_controller.dart';
import 'package:ds_admin/framework/utils/extension/extension.dart';
import 'package:ds_admin/framework/utils/extension/string_extension.dart';
import 'package:ds_admin/ui/utils/theme/app_colors.dart';
import 'package:ds_admin/ui/utils/theme/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:transliteration/response/transliteration_response.dart';
import 'package:transliteration/transliteration.dart';

import '../../../framework/repository/member_management/model/get_members_response.dart';

class AddEditMemberDialog extends ConsumerStatefulWidget {
  final Member? member;
  const AddEditMemberDialog({this.member, super.key});


  @override
  ConsumerState<AddEditMemberDialog> createState() => _AddEditMemberDialogState();
}

class _AddEditMemberDialogState extends ConsumerState<AddEditMemberDialog> {



  @override
  void initState() {
    super.initState();
    SchedulerBinding.instance.addPostFrameCallback((timestamp) async {
      if (widget.member != null) {
        // Load member data if editing
        ref.read(memberManagementController).loadMemberData(widget.member!);
      }else{
        var controller = ref.read(memberManagementController);
        await controller.getStatesApiCall();
        controller.clearMemberData(isAddMode: true);
        // Explicitly restore previous selections after states are loaded
        await controller.restorePreviousSelectionsForAddMode();
      }
    });

  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var controller = ref.watch(memberManagementController);
    return AlertDialog(
      title: Text(widget.member == null ? LocaleKeys.keyAddMember.localized : LocaleKeys.keyEditMember.localized),
      content: Container(
        constraints: BoxConstraints(minWidth: 400, maxWidth: 420),
        child: SingleChildScrollView(
          child: Form(
            key: controller.memberFormKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildSectionTitle(LocaleKeys.keyBasicInformation.localized),
                const SizedBox(height: 16),
                _buildTextField(
                  controller: controller.memberNameController,
                  label: '${LocaleKeys.keyMemberName.localized}*',
                  validator: (value) => value?.isEmpty ?? true ? 'Required' : null,
                  onChanged: (value) async {
                    if(value.length > 3){
                      // Map for Hindi and Gujarati characters
                      String? hindiText = '';
                      String? gujaratiText = '';
                      TransliterationResponse? response = await Transliteration.transliterate(value, Languages.HINDI);
                      hindiText = response?.transliterationSuggestions.first.toString();

                      TransliterationResponse? responseGuj = await Transliteration.transliterate(value, Languages.GUJARATI);
                      gujaratiText = responseGuj?.transliterationSuggestions.first.toString();


                      // Update controllers
                      controller.memberNameHiController.text = hindiText ?? '';
                      controller.memberNameGjController.text = gujaratiText ?? '';
                    }else{
                      controller.memberNameHiController.text = '';
                      controller.memberNameGjController.text = '';
                    }
                  }
                ),
                const SizedBox(height: 12),
                _buildTextField(
                  controller: controller.memberNameHiController,
                  label: '${LocaleKeys.keyMemberName.localized} (Hindi)*',
                  validator: (value) => value?.isEmpty ?? true ? 'Required' : null,
                ),
                const SizedBox(height: 12),
                _buildTextField(
                  controller: controller.memberNameGjController,
                  label: '${LocaleKeys.keyMemberName.localized} (Gujarati)',
                  validator: (value) => value?.isEmpty ?? true ? 'Required' : null,
                ),
                const SizedBox(height: 12),
                _buildTextField(
                  controller: controller.nickNameController,
                  label: LocaleKeys.keyNickName.localized,
                  validator: (value) => value?.isEmpty ?? true ? 'Required' : null,
                  onChanged: (value) async {
                    if(value.length > 1){
                      // Map for Hindi and Gujarati characters
                      String? hindiText = '';
                      String? gujaratiText = '';
                      TransliterationResponse? response0 = await Transliteration.transliterate(value, Languages.HINDI);
                      hindiText = response0?.transliterationSuggestions.first.toString();

                      TransliterationResponse? responseGuj0 = await Transliteration.transliterate(value, Languages.GUJARATI);
                      gujaratiText = responseGuj0?.transliterationSuggestions.first.toString();


                      // Update controllers
                      controller.nickNameHiController.text = hindiText ?? '';
                      controller.nickNameGjController.text = gujaratiText ?? '';
                    }else{
                      controller.nickNameHiController.text = '';
                      controller.nickNameGjController.text = '';
                    }
                  },
                ),
                const SizedBox(height: 12),
                _buildTextField(
                  controller: controller.nickNameHiController,
                  label: '${LocaleKeys.keyNickName.localized} (Hindi)',
                  validator: (value) => value?.isEmpty ?? true ? 'Required' : null,
                ),
                const SizedBox(height: 12),
                _buildTextField(
                  controller: controller.nickNameGjController,
                  label: '${LocaleKeys.keyNickName.localized} (Gujarati)',
                  validator: (value) => value?.isEmpty ?? true ? 'Required' : null,
                ),

                const SizedBox(height: 24),
                _buildSectionTitle(LocaleKeys.keyContactInformation.localized),
                const SizedBox(height: 16),
                _buildTextField(
                  controller: controller.mobileController,
                  label: LocaleKeys.keyMobileNumber.localized,
                  keyboardType: TextInputType.phone,
                  maxLength: 10,
                  validator: (value){
                    if((value ?? '').isNotEmpty && value?.length != 10){
                      return 'Mobile number should be 10 digits';
                    }
                    return null;
                  },
                  // validator: (value) => value?.isEmpty ?? true ? 'Required' : null,
                ),
                const SizedBox(height: 12),
                _buildTextField(
                  controller: controller.alternateMobileController,
                  label: LocaleKeys.keyAlternateNumber.localized,
                  keyboardType: TextInputType.phone,
                  maxLength: 10,
                  validator: (value){
                    if((value ?? '').isNotEmpty && value?.length != 10){
                      return 'Mobile number should be 10 digits';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 12),
                _buildTextField(
                  controller: controller.addressController,
                  label: LocaleKeys.keyAddress.localized,
                  maxLines: 3,
                ),

                const SizedBox(height: 24),
                _buildSectionTitle(LocaleKeys.keyLocationInformation.localized),
                const SizedBox(height: 16),
                _buildDropdown(
                  label: '${LocaleKeys.keyState.localized}*',
                  value: controller.selectedStateId,
                  items: controller.states.map((state) {
                    return DropdownMenuItem<int>(
                      value: state.stateId,
                      child: Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 5,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: AppColors.greyF6F6F6,
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              state.stateCode ?? '',
                              style: TextStyle(
                                color: Colors.blue[800],
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                            ),
                          ),
                          const SizedBox(width: 5),
                          Text(state.stateName ?? ''),
                        ],
                      ),
                    );
                  }).toList(),
                  onChanged: (value) => controller.updateMemberSelectedState(value),
                ),
                const SizedBox(height: 12),
                _buildSearchableCityDropdown(controller),
                const SizedBox(height: 12),
                _buildTextField(
                  controller: controller.pincodeController,
                  label: '${LocaleKeys.keyPincode.localized}*',
                  keyboardType: TextInputType.number,
                  maxLength: 6,
                  validator: (value) => value?.isEmpty ?? true ? 'Required' : null,
                ),
                const SizedBox(height: 12),
                _buildTextField(
                  controller: controller.panchCityController,
                  label: LocaleKeys.keyPanchCity.localized,
                  validator: (value) => value?.isEmpty ?? true ? 'Required' : null,
                ),

                const SizedBox(height: 24),
                _buildSectionTitle(LocaleKeys.keyFamilyInformation.localized),
                const SizedBox(height: 16),

                // father autocomplete
                Consumer(builder: (BuildContext context, WidgetRef ref, Widget? child) {
                  var selectedFatherWatch = ref.watch(memberManagementController);
                  return _buildMemberAutocomplete(
                      label: LocaleKeys.keyFather.localized,
                      selectedValue: selectedFatherWatch.selectedFather,
                      items: controller.fatherSearchList,
                      onSelected: (value) => controller.updateMemberFather(value),
                      onSearch: (value) {
                        if (value.isEmpty) {
                          controller.updateMemberFather(null);
                        } else if (value.length > 3) {
                          controller.getMembersFatherList(value);
                        } else {
                          controller.updateMemberFather(null);
                        }
                      },
                      textController: controller.ctrlSearchFather
                  );
                },),
                const SizedBox(height: 12),

                // mother autocomplete
                Consumer(builder: (BuildContext context, WidgetRef ref, Widget? child){
                  var selectedMotherWatch = ref.watch(memberManagementController);
                  return _buildMemberAutocomplete(
                      label: LocaleKeys.keyMother.localized,
                      selectedValue: selectedMotherWatch.selectedMother,
                      items: controller.motherSearchList,
                      onSelected: (value) => controller.updateMemberMother(value),
                      onSearch: (value) {
                        if (value.isEmpty) {
                          controller.updateMemberMother(null);
                        } else if (value.length > 3) {
                          controller.getMembersMotherList(value);
                        } else {
                          controller.updateMemberMother(null);
                        }
                      },
                      textController: controller.ctrlSearchMother
                  );
                }),
                const SizedBox(height: 12),

                // spouse autocomplete
                Consumer(builder: (BuildContext context, WidgetRef ref, Widget? child){
                  var selectedSpouseWatch = ref.watch(memberManagementController);
                  return _buildMemberAutocomplete(
                      label: LocaleKeys.keySpouse.localized,
                      selectedValue: selectedSpouseWatch.selectedSpouse,
                      items: controller.spouseSearchList,
                      onSelected: (value) => controller.updateMemberSpouse(value),
                      onSearch: (value) {
                        if (value.isEmpty) {
                          controller.updateMemberSpouse(null);
                        } else if (value.length > 3) {
                          controller.getMembersSpouseList(value);
                        } else {
                          controller.updateMemberSpouse(null);
                        }
                      },
                      textController: controller.ctrlSearchSpouse
                  );
                }),

                const SizedBox(height: 24),
                _buildSectionTitle(LocaleKeys.keyPersonalInformation.localized),
                const SizedBox(height: 16),
                _buildTextField(
                  controller: controller.emailController,
                  label: LocaleKeys.keyEmail.localized,
                  keyboardType: TextInputType.emailAddress,
                ),
                const SizedBox(height: 12),
                _buildDatePicker(
                  controller: controller.dobController,
                  label: LocaleKeys.keyDateofBirth.localized,
                  onTap: () =>  widget.member == null ? controller.selectDate(context) : controller.selectDate(context, initialDate: controller.dobController.text),
                ),
                const SizedBox(height: 12),
                _buildDropdown(
                  label: "${LocaleKeys.keyMaritalStatus.localized}*",
                  value: controller.selectedMaritalStatus,
                  items: controller.martialStatusList.map((String value) {
                    return DropdownMenuItem<String>(
                      value: value,
                      child: Text(value),
                    );
                  }).toList(),
                  onChanged: (value) => controller.updateMemberMaritalStatus(value),
                ),

                const SizedBox(height: 24),
                _buildSectionTitle(LocaleKeys.keyEducationalInformation.localized),
                const SizedBox(height: 16),
                _buildTextField(
                  controller: controller.graduationController,
                  label: LocaleKeys.keyGraduation.localized,
                ),
                const SizedBox(height: 12),
                _buildTextField(
                  controller: controller.schoolCollegeNameController,
                  label: LocaleKeys.keySchoolCollegeName.localized,
                ),
                const SizedBox(height: 12),
                _buildTextField(
                  controller: controller.hscController,
                  label: LocaleKeys.keyHSC.localized,
                ),
                const SizedBox(height: 12),
                _buildTextField(
                  controller: controller.sscController,
                  label: LocaleKeys.keySSC.localized,
                ),

                const SizedBox(height: 24),
                _buildSectionTitle(LocaleKeys.keyProfessionalInfo.localized),
                const SizedBox(height: 16),
                _buildTextField(
                  controller: controller.occupationController,
                  label: LocaleKeys.keyOccupation.localized,
                ),
                const SizedBox(height: 12),
                _buildTextField(
                  controller: controller.companyFirmNameController,
                  label: LocaleKeys.keyCompanyFirm.localized,
                ),
                const SizedBox(height: 12),
                _buildTextField(
                  controller: controller.experienceController,
                  label: LocaleKeys.keyExperience.localized,
                ),
                const SizedBox(height: 12),
                _buildTextField(
                  controller: controller.otherBusinessController,
                  label: LocaleKeys.keyOtherBusiness.localized,
                ),

                const SizedBox(height: 24),
                _buildSectionTitle(LocaleKeys.keyOtherInformation.localized),
                const SizedBox(height: 16),
                _buildDropdown(
                  label: '${LocaleKeys.keyStatus.localized}*',
                  value: controller.selectedStatus,
                  items: controller.statusOptions.map((String value) {
                    return DropdownMenuItem<String>(
                      value: value,
                      child: Text(value),
                    );
                  }).toList(),
                  onChanged: (value) => controller.updateSelectedMemberStatus(value),
                ),
                const SizedBox(height: 12),
                SwitchListTile(
                  title: Text(LocaleKeys.keyIsPhysicallyChallenged.localized),
                  value: controller.isPhysicallyChallenged,
                  activeColor: Colors.white,
                  activeTrackColor: Colors.red[300],
                  inactiveThumbColor: Colors.white,
                  inactiveTrackColor: Colors.green[600],
                  trackOutlineColor: WidgetStateProperty.resolveWith(
                        (states) => Colors.transparent,
                  ),
                  thumbIcon: WidgetStateProperty.resolveWith<Icon?>(
                        (Set<WidgetState> widgetStates) {
                      if (widgetStates.contains(WidgetState.selected)) {
                        return const Icon(Icons.sick, color: Colors.red, size: 16);
                      }
                      return const Icon(Icons.check_circle, color: Colors.green, size: 16);
                    },
                  ),
                  onChanged: controller.updatePhysicallyChallenged,
                ),
                if (controller.isPhysicallyChallenged) ...[
                  const SizedBox(height: 12),
                  _buildTextField(
                    controller: controller.physicalChallengedDetailsController,
                    label: LocaleKeys.keyPhysicallyChallengedDetails.localized,
                  ),
                ],
                const SizedBox(height: 12),
                _buildDropdown(
                  label: '${LocaleKeys.keyGender.localized}*',
                  value: controller.selectedGender,
                  items: controller.genderOptions.map((String value) {
                    return DropdownMenuItem<String>(
                      value: value,
                      child: Text(value),
                    );
                  }).toList(),
                  onChanged: (value) => controller.updateSelectedGender(value),
                ),
                const SizedBox(height: 12),

                // children auto complete
                _buildMemberAutocomplete(
                    label: LocaleKeys.keySearchChildrentoadd.localized,
                    selectedValue: null,
                    items: controller.childrenSearchList,
                    onSelected: (value) => controller.addMemberChildren(value),
                    onSearch: (value) => value.length > 3 ? controller.getMembersChildrenList(value) : null,
                    textController: controller.ctrlSearchChildren
                ),
                const SizedBox(height: 12),
                // Children
                if (controller.selectedChildren.isNotEmpty) ...[
                  Container(
                    padding: EdgeInsets.all(8.w),
                    constraints: BoxConstraints(
                      maxWidth: 420,
                      minWidth: 400,
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.greyF6F6F6,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: AppColors.greyF8F8F8),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          LocaleKeys.keyChildren.localized,
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: AppColors.primary,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Wrap(
                          spacing: 8,
                          runSpacing: 8,
                          children: controller.selectedChildren.map((child) {
                            return AnimatedSize(
                              duration: const Duration(milliseconds: 300),
                              child: Material(
                                color: AppColors.white,
                                child: Chip(
                                  color: WidgetStateProperty.all(AppColors.primary),
                                  label: Text(
                                    child.name ?? '',
                                    style: const TextStyle(
                                      color: AppColors.white,
                                    ),
                                  ),
                                  avatar: CircleAvatar(
                                    backgroundColor: AppColors.ascent,
                                    child: Text(
                                      (child.name?[0] ?? '').toUpperCase(),
                                      style: TextStyle(
                                        color: AppColors.primary,
                                        fontSize: 12.sp,
                                      ),
                                    ),
                                  ).paddingSymmetric(vertical: 3),
                                  deleteIcon: const Icon(
                                    Icons.cancel,
                                    size: 18,
                                    color: AppColors.red,
                                  ),
                                  onDeleted: () {
                                    controller.selectedChildren.remove(child);
                                    controller.updateLoadingStatus(false);
                                  },
                                  backgroundColor: AppColors.primary.withValues(alpha: 0.05),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(20),
                                    side: BorderSide(
                                      color: AppColors.primary.withValues(alpha: 0.2),
                                    ),
                                  ),
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 5,
                                    vertical: 3,
                                  ),
                                ),
                              ),
                            );
                          }).toList(),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 12),
                ],


              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
              ref.read(memberManagementController).clearMemberData();
              Navigator.of(context).pop();
            },
          child: Text(LocaleKeys.keyCancel.localized),
        ),
        FilledButton(
          onPressed: () {
            if (controller.memberFormKey.currentState!.validate()) {
              if(widget.member != null){
                ref.read(memberManagementController).updateMemberApiCall(context, ref, widget.member!.memberId!);
              }else{
                ref.read(memberManagementController).addMemberApiCall(context, ref);
              }
            }
          },
          child: Text(LocaleKeys.keySave.localized),
        ),
      ],
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.bold,
        color: AppColors.primary,
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    TextInputType? keyboardType,
    int? maxLength,
    ValueChanged<String>? onChanged,
    String? Function(String?)? validator,
    int? maxLines
  }) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        border: const OutlineInputBorder(),
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
      ),
      keyboardType: keyboardType,
      maxLength: maxLength,
      validator: validator,
      onChanged: onChanged,
      maxLines: maxLines,
    );
  }

  Widget _buildDropdown({
    required String label,
    required dynamic value,
    required List<DropdownMenuItem> items,
    required Function(dynamic) onChanged,
  }) {
    return DropdownButtonFormField2(
      decoration: InputDecoration(
        labelText: label,
        border: const OutlineInputBorder(),
        contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 16),
      ),
      items: items,
      value: value,
      onChanged: onChanged,
      dropdownStyleData: DropdownStyleData(
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            color: AppColors.white
        ),
      ),
      menuItemStyleData: MenuItemStyleData(
        height: 40,
        overlayColor: WidgetStateProperty.all(AppColors.white),
      ),
    );
  }

  Widget _buildDatePicker({
    required TextEditingController controller,
    required String label,
    required VoidCallback onTap,
  }) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        border: const OutlineInputBorder(),
        suffixIcon: const Icon(Icons.calendar_today),
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
      ),
      readOnly: true,
      onTap: onTap,
    );
  }

  Widget _buildMemberAutocomplete({
    required String label,
    required Member? selectedValue,
    required List<Member> items,
    required Function(Member?) onSelected,
    required Function(String) onSearch,
    required TextEditingController textController
  }) {
    String initialText = '';
    // Set initial text if value exists
    if (selectedValue != null) {
      initialText = selectedValue.memberName ?? '';
    }else{
      initialText = '';
    }

    return Autocomplete<Member>(
      initialValue: TextEditingValue(text: initialText),
      optionsBuilder: (TextEditingValue textEditingValue) {
        // Always call onSearch to handle clearing when text is empty
        onSearch(textEditingValue.text);

        if (textEditingValue.text.isEmpty) {
          return const Iterable<Member>.empty();
        }

        return items.where((Member option) {
          return option.memberName?.toLowerCase().contains(
            textEditingValue.text.toLowerCase(),
          ) ?? false;
        });
      },
      onSelected: (Member selection) {
        onSelected(selection);
      },
      displayStringForOption: (Member option) => option.memberName ?? '',
      fieldViewBuilder: (
          BuildContext context,
          TextEditingController fieldTextEditingController,
          FocusNode fieldFocusNode,
          VoidCallback onFieldSubmitted,
          ) {
        return TextFormField(
          controller: fieldTextEditingController,
          focusNode: fieldFocusNode,
          decoration: InputDecoration(
            labelText: label,
            border: const OutlineInputBorder(),
            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
            suffixIcon: const Icon(Icons.search),
          ),
        );
      },
      optionsViewBuilder: (
          BuildContext context,
          AutocompleteOnSelected<Member> onSelected,
          Iterable<Member> options,
          ) {
        return Align(
          alignment: Alignment.topLeft,
          child: Material(
            elevation: 4.0,
            child: ConstrainedBox(
              constraints: BoxConstraints(maxHeight: 200, maxWidth: 420, minWidth: 400),
              child: ListView.builder(
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                itemCount: options.length,
                itemBuilder: (BuildContext context, int index) {
                  final Member option = options.elementAt(index);
                  return ListTile(
                    tileColor: AppColors.white,
                    title: Text('${option.memberName} | ${option.age} | ${option.state} | ${option.city}'),
                    onTap: () {
                      onSelected(option);
                    },
                  );
                },
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildSearchableCityDropdown(MemberManagementController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Search field
        TextFormField(
          controller: controller.citySearchController,
          decoration: InputDecoration(
            labelText: '${LocaleKeys.keyCity.localized}* (Search)',
            border: const OutlineInputBorder(),
            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
            suffixIcon: controller.citySearchQuery.isNotEmpty
                ? IconButton(
                    icon: const Icon(Icons.clear),
                    onPressed: () => controller.clearCitySearch(),
                  )
                : const Icon(Icons.search),
          ),
          onChanged: (value) => controller.updateCitySearchQuery(value),
        ),
        const SizedBox(height: 8),
        // Dropdown for filtered cities
        DropdownButtonFormField2<int>(
          decoration: InputDecoration(
            labelText: controller.selectedCityId != null
                ? 'Selected City'
                : 'Select from filtered results',
            border: const OutlineInputBorder(),
            contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 16),
          ),
          value: controller.selectedCityId,
          items: controller.filteredCities.map((city) {
            return DropdownMenuItem<int>(
              value: city.cityId,
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 5,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.greyF6F6F6,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      city.cityCode ?? '',
                      style: TextStyle(
                        color: Colors.blue[800],
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
                  const SizedBox(width: 5),
                  Expanded(
                    child: Text(
                      city.cityName ?? '',
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
          onChanged: (value) => controller.updateMemberSelectedCity(value),
          dropdownStyleData: DropdownStyleData(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4),
              color: AppColors.white,
            ),
            maxHeight: 200,
          ),
          menuItemStyleData: MenuItemStyleData(
            height: 40,
            overlayColor: WidgetStateProperty.all(AppColors.white),
          ),
          validator: (value) => value == null ? 'Please select a city' : null,
        ),
        // Show results count
        if (controller.cities.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Text(
              controller.citySearchQuery.isEmpty
                  ? 'Showing ${controller.filteredCities.length} cities (sorted A-Z)'
                  : 'Found ${controller.filteredCities.length} cities matching "${controller.citySearchQuery}"',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
      ],
    );
  }

}