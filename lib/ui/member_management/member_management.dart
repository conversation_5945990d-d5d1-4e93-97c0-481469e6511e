import 'package:ds_admin/ui/member_management/mobile/member_management_mobile.dart';
import 'package:ds_admin/ui/member_management/web/member_management_web.dart';
import 'package:responsive_builder/responsive_builder.dart';

import '../utils/theme/theme.dart';

class MemberManagement extends StatefulWidget {
  const MemberManagement({super.key});

  @override
  State<MemberManagement> createState() => _MemberManagementState();
}

class _MemberManagementState extends State<MemberManagement> {
  @override
  Widget build(BuildContext context) {
    return ScreenTypeLayout.builder(
      mobile: (BuildContext context) {
        return const MemberManagementMobile();
      },
      desktop: (BuildContext context) {
        return const MemberManagementWeb();
      },
    );
  }
}
