import 'package:animated_text_kit/animated_text_kit.dart';
import 'package:ds_admin/framework/utils/extension/string_extension.dart';
import 'package:ds_admin/framework/utils/session.dart';
import 'package:ds_admin/ui/utils/theme/locale_keys.g.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/scheduler.dart';
import 'package:ds_admin/ui/routing/navigation_stack_item.dart';
import 'package:ds_admin/ui/routing/stack.dart';
import 'package:ds_admin/ui/utils/theme/device_configuration.dart';
import 'package:ds_admin/ui/utils/theme/theme.dart';

class SplashMobile extends ConsumerStatefulWidget {
  const SplashMobile({super.key});

  @override
  ConsumerState<SplashMobile> createState() => _SplashMobileState();
}

class _SplashMobileState extends ConsumerState<SplashMobile> {
  ///Init Override
  @override
  void initState() {
    super.initState();
    SchedulerBinding.instance.addPostFrameCallback((timeStamp)  async {
      var session = Session.getUserAccessToken();
      Future.delayed(const Duration(seconds: 3), (){
        if(session.isEmpty){
          ref.read(navigationStackController).pushAndRemoveAll(const NavigationStackItem.login());
        }else{
          ref.read(navigationStackController).pushAndRemoveAll(const NavigationStackItem.dashboard());
        }

      });
    });
  }

  ///Dispose Override
  @override
  void dispose() {
    super.dispose();
  }

  ///Build Override
  @override
  Widget build(BuildContext context) {
    mobileDeviceConfiguration(context);
    return Scaffold(
      backgroundColor: AppColors.white,
      body: _bodyWidget(context),
    );
  }

  ///Body Widget
  Widget _bodyWidget(BuildContext ctx) {
    return Center(
      child: SizedBox(
        width: 250.0,
        child: DefaultTextStyle(
          style: TextStyles.extraBold.copyWith(fontSize: 34, color: AppColors.primary),
          child: AnimatedTextKit(
            animatedTexts: [
              TypewriterAnimatedText(LocaleKeys.keyWelcomeTO.localized, speed: Duration(milliseconds: 100)),
            ],
          ),
        ),
      ),
    );
  }
}
