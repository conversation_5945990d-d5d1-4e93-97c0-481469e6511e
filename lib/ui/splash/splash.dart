import 'package:flutter/material.dart';
import 'package:responsive_builder/responsive_builder.dart';
import 'package:ds_admin/ui/splash/mobile/splash_mobile.dart';
import 'package:ds_admin/ui/splash/web/splash_web.dart';

class Splash extends StatelessWidget {
  const Splash({super.key});

  ///Build Override
  @override
  Widget build(BuildContext context) {
    return ScreenTypeLayout.builder(
        mobile: (BuildContext context) {
          return const SplashMobile();
        },
        desktop: (BuildContext context) {
          return const SplashWeb();
        }
    );
  }
}

