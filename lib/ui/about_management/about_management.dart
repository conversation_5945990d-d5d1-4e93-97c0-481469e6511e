import 'package:ds_admin/ui/about_management/web/about_management_web.dart';
import 'package:responsive_builder/responsive_builder.dart';

import '../utils/theme/theme.dart';
import 'mobile/about_management_mobile.dart';

class AboutManagement extends StatefulWidget {
  const AboutManagement({super.key});

  @override
  State<AboutManagement> createState() => _AboutManagementState();
}

class _AboutManagementState extends State<AboutManagement> {
  @override
  Widget build(BuildContext context) {
    return ScreenTypeLayout.builder(
      mobile: (BuildContext context) => const AboutManagementMobile(),
      tablet: (BuildContext context) => const AboutManagementWeb(),
      desktop: (BuildContext context) => const AboutManagementWeb(),
    );
  }
}
