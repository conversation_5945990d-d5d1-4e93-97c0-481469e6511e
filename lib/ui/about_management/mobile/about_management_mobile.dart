import 'package:ds_admin/framework/utils/extension/extension.dart';
import 'package:ds_admin/framework/utils/extension/string_extension.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:html_editor_enhanced/html_editor.dart';

import '../../../framework/controller/about_management/about_management_controller.dart';
import '../../../framework/controller/sidebar/sidebar_controller.dart';
import '../../routing/navigation_stack_item.dart';
import '../../utils/theme/locale_keys.g.dart';
import '../../utils/theme/theme.dart';
import '../../utils/widgets/base_scaffold.dart';
import '../../utils/widgets/change_language_widget.dart';
import '../../utils/widgets/common_text.dart';
import '../../utils/widgets/dialog_progressbar.dart';

class AboutManagementMobile extends ConsumerStatefulWidget {
  const AboutManagementMobile({super.key});

  @override
  ConsumerState<AboutManagementMobile> createState() => _AboutManagementMobileState();
}

class _AboutManagementMobileState extends ConsumerState<AboutManagementMobile> {

  @override
  void initState() {
    super.initState();
    SchedulerBinding.instance.addPostFrameCallback((timeStamp) {
      final aboutController = ref.read(aboutManagementController);
      aboutController.getAboutApiCall(context: context);
      ref.read(sidebarController).updateDrawerStatus(0);
    });
  }

  @override
  Widget build(BuildContext context) {
    var aboutController = ref.watch(aboutManagementController);
    return BaseScaffold(
      route: NavigationStackItem.aboutManagement(),
      isMobile: true,
      appbar: AppBar(
        title: CommonText(title: LocaleKeys.keyAbout.localized, textStyle: TextStyles.medium.copyWith(color: AppColors.white),),
        centerTitle: true,
        backgroundColor: AppColors.primary,
        actions: [
          ChangeLanguageWidget(ref: ref,).paddingOnly(right: 20),
        ],
      ),
      body: Stack(
        children: [
          Container(
            color: AppColors.white,
            height: MediaQuery.of(context).size.height,
            width: MediaQuery.of(context).size.width,
            child: Column(
              children: [
                Expanded(
                  child: Card(
                    child: SingleChildScrollView(
                      child: Form(
                        key: aboutController.formKey,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const SizedBox(height: 16),
                            TextFormField(
                              controller: aboutController.ctrlAboutTitle,
                              decoration: InputDecoration(
                                labelText: LocaleKeys.keyAboutTitle.localized,
                              ),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return LocaleKeys.keyErrAboutTitle.localized;
                                }
                                return null;
                              },
                            ),
                            const SizedBox(height: 16),
                            Text(LocaleKeys.keyDsAbout.localized,
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Consumer(
                              builder: (BuildContext context, WidgetRef ref, Widget? child) {
                                var controller = ref.watch(sidebarController);
                                return SizedBox(
                                  height: 550.h,
                                  child: controller.isDrawerOpen == 1 ? SizedBox() : HtmlEditor(
                                    controller: aboutController.htmlEditorController,
                                    htmlEditorOptions: HtmlEditorOptions(
                                      hint: LocaleKeys.keyErrDsAbout.localized,
                                      initialText: aboutController.extractTextFromHtml(aboutController.rulesResponse.data?.description ?? ''),
                                    ),
                                    htmlToolbarOptions: const HtmlToolbarOptions(
                                      defaultToolbarButtons: [
                                        ColorButtons(),
                                        ParagraphButtons(lineHeight: false, caseConverter: false),
                                        InsertButtons(
                                          link: true,
                                        ),
                                        OtherButtons(),
                                      ],
                                      toolbarType: ToolbarType.nativeGrid,
                                    ),
                                  ),
                                );
                              },
                            ),
                            const SizedBox(height: 16),
                            ElevatedButton(
                              onPressed: (){
                                aboutController.updateAbout();
                              },
                              style: ButtonStyle(
                                fixedSize: WidgetStateProperty.all(Size(200, 35)),
                                backgroundColor: WidgetStateProperty.all(AppColors.greyF6F6F6),
                              ),
                              child: Text(
                                LocaleKeys.keyUpdate.localized,
                                style: TextStyle(color: AppColors.primary),
                              ),
                            ).alignAtCenter(),
                          ],
                        ),
                      ).paddingSymmetric(horizontal: 16, vertical: 16),
                    ),
                  ).paddingSymmetric(horizontal: 20, vertical: 20),
                ),
              ],
            ),
          ),
          DialogProgressBar(isLoading: aboutController.isLoading),
        ],
      ),
    );
  }
}
