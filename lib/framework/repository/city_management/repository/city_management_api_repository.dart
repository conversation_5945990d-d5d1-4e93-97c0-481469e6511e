import 'package:ds_admin/framework/repository/city_management/contract/city_management_repository.dart';
import 'package:ds_admin/framework/repository/city_management/model/add_city_request.dart';
import 'package:ds_admin/framework/repository/city_management/model/delete_city_request.dart';
import 'package:ds_admin/framework/repository/city_management/model/get_cities_response.dart';
import 'package:ds_admin/framework/repository/city_management/model/update_city_request.dart';
import 'package:ds_admin/framework/repository/common_message_response.dart';
import 'package:ds_admin/framework/repository/state_management/model/get_states_response.dart';
import 'package:ds_admin/ui/utils/const/app_constants.dart';
import 'package:injectable/injectable.dart';
import '../../../provider/network/network.dart';

@LazySingleton(as: CityManagementRepository, env: [production, development])
class CityManagementApiRepository extends CityManagementRepository{
  final DioClient apiClient;
  CityManagementApiRepository(this.apiClient);

  @override
  Future addCity(AddCityRequest request) async{
    try {
      Response? response = await apiClient.postRequest(ApiEndPoints.addCity, request.toJson(), isTokenRequiredInHeader: true);
      CommonMessageResponse responseModel = commonMessageResponseFromJson(response.toString());
      if (response?.statusCode == ApiEndPoints.apiStatus_200) {
        return ApiResult.success(data: responseModel);
      } else {
        return ApiResult.failure(error: NetworkExceptions.defaultError(response?.statusMessage??''));
      }
    } catch (err) {
      return ApiResult.failure(error: NetworkExceptions.getDioException(err));
    }
  }

  @override
  Future deleteCity(int cityId) async {
    DeleteCityRequest request = DeleteCityRequest(cityId: cityId);
    try {
      Response? response = await apiClient.deleteRequest(ApiEndPoints.deleteCity, request.toJson(), isTokenRequiredInHeader: true);
      CommonMessageResponse responseModel = commonMessageResponseFromJson(response.toString());
      if (response?.statusCode == ApiEndPoints.apiStatus_200) {
        return ApiResult.success(data: responseModel);
      } else {
        return ApiResult.failure(error: NetworkExceptions.defaultError(response?.statusMessage??''));
      }
    } catch (err) {
      return ApiResult.failure(error: NetworkExceptions.getDioException(err));
    }
  }

  @override
  Future getCityList() async {
    try {
      Response? response = await apiClient.getRequest(ApiEndPoints.getCities, isTokenRequiredInHeader: true);
      GetCitiesResponse responseModel = getCitiesResponseFromJson(response.toString());
      if (response?.statusCode == ApiEndPoints.apiStatus_200) {
        return ApiResult.success(data: responseModel);
      } else {
        return ApiResult.failure(error: NetworkExceptions.defaultError(response?.statusMessage??''));
      }
    } catch (err) {
      return ApiResult.failure(error: NetworkExceptions.getDioException(err));
    }
  }

  @override
  Future updateCity(UpdateCityRequest request) async {
    try{
      Response? response = await apiClient.putRequest(ApiEndPoints.updateCity, request.toJson(), isTokenRequiredInHeader: true);
      CommonMessageResponse responseModel = commonMessageResponseFromJson(response.toString());
      if (response?.statusCode == ApiEndPoints.apiStatus_200) {
        return ApiResult.success(data: responseModel);
      } else {
        return ApiResult.failure(error: NetworkExceptions.defaultError(response?.statusMessage??''));
      }
    } catch (err) {
      return ApiResult.failure(error: NetworkExceptions.getDioException(err));
    }
  }

  @override
  Future getActiveStatesList() async{
    try{
      Response? response = await apiClient.getRequest(ApiEndPoints.getActiveStates, isTokenRequiredInHeader: true);
      GetStatesResponse responseModel = getStatesResponseFromJson(response.toString());
      if (response?.statusCode == ApiEndPoints.apiStatus_200) {
        return ApiResult.success(data: responseModel);
      } else {
        return ApiResult.failure(error: NetworkExceptions.defaultError(response?.statusMessage??''));
      }
    } catch (err) {
      return ApiResult.failure(error: NetworkExceptions.getDioException(err));
    }

  }

}