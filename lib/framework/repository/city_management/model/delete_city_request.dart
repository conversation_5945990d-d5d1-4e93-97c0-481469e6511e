// To parse this JSON data, do
//
//     final deleteCityRequest = deleteCityRequestFromJson(jsonString);

import 'package:json_annotation/json_annotation.dart';
import 'dart:convert';

part 'delete_city_request.g.dart';

DeleteCityRequest deleteCityRequestFromJson(String str) => DeleteCityRequest.fromJson(json.decode(str));

String deleteCityRequestToJson(DeleteCityRequest data) => json.encode(data.toJson());

@JsonSerializable()
class DeleteCityRequest {
  @JsonKey(name: "city_id")
  int? cityId;

  DeleteCityRequest({
    this.cityId,
  });

  factory DeleteCityRequest.fromJson(Map<String, dynamic> json) => _$DeleteCityRequestFromJson(json);

  Map<String, dynamic> toJson() => _$DeleteCityRequestToJson(this);
}
