// To parse this JSON data, do
//
//     final getCitiesResponse = getCitiesResponseFromJson(jsonString);

import 'package:json_annotation/json_annotation.dart';
import 'dart:convert';

part 'get_cities_response.g.dart';

GetCitiesResponse getCitiesResponseFromJson(String str) => GetCitiesResponse.fromJson(json.decode(str));

String getCitiesResponseToJson(GetCitiesResponse data) => json.encode(data.toJson());

@JsonSerializable()
class GetCitiesResponse {
  @JsonKey(name: "status")
  bool? status;
  @JsonKey(name: "message")
  String? message;
  @JsonKey(name: "data")
  Data? data;

  GetCitiesResponse({
    this.status,
    this.message,
    this.data,
  });

  factory GetCitiesResponse.fromJson(Map<String, dynamic> json) => _$GetCitiesResponseFromJson(json);

  Map<String, dynamic> toJson() => _$GetCitiesResponseToJson(this);
}

@JsonSerializable()
class Data {
  @JsonKey(name: "cities")
  List<City>? cities;

  Data({
    this.cities,
  });

  factory Data.fromJson(Map<String, dynamic> json) => _$DataFromJson(json);

  Map<String, dynamic> toJson() => _$DataToJson(this);
}

@JsonSerializable()
class City {
  @JsonKey(name: "state_id")
  int? stateId;
  @JsonKey(name: "state_name")
  String? stateName;
  @JsonKey(name: "city_id")
  int? cityId;
  @JsonKey(name: "city_name")
  String? cityName;
  @JsonKey(name: "city_code")
  String? cityCode;
  @JsonKey(name: "status")
  String? status;

  City({
    this.stateId,
    this.stateName,
    this.cityId,
    this.cityName,
    this.cityCode,
    this.status,
  });

  factory City.fromJson(Map<String, dynamic> json) => _$CityFromJson(json);

  Map<String, dynamic> toJson() => _$CityToJson(this);
}
