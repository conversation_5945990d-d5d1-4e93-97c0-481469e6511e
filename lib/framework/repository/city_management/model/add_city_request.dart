// To parse this JSON data, do
//
//     final addCityRequest = addCityRequestFromJson(jsonString);

import 'package:json_annotation/json_annotation.dart';
import 'dart:convert';

part 'add_city_request.g.dart';

AddCityRequest addCityRequestFromJson(String str) => AddCityRequest.fromJson(json.decode(str));

String addCityRequestToJson(AddCityRequest data) => json.encode(data.toJson());

@JsonSerializable()
class AddCityRequest {
  @JsonKey(name: "state_id")
  int? stateId;
  @J<PERSON><PERSON><PERSON>(name: "city_name")
  String? cityName;
  @JsonKey(name: "city_code")
  String? cityCode;
  @JsonKey(name: "status")
  String? status;

  AddCityRequest({
    this.stateId,
    this.cityName,
    this.cityCode,
    this.status,
  });

  factory AddCityRequest.fromJson(Map<String, dynamic> json) => _$AddCityRequestFromJson(json);

  Map<String, dynamic> toJson() => _$AddCityRequestToJson(this);
}
