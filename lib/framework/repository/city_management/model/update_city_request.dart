// To parse this JSON data, do
//
//     final updateCityRequest = updateCityRequestFromJson(jsonString);

import 'package:json_annotation/json_annotation.dart';
import 'dart:convert';

part 'update_city_request.g.dart';

UpdateCityRequest updateCityRequestFromJson(String str) => UpdateCityRequest.fromJson(json.decode(str));

String updateCityRequestToJson(UpdateCityRequest data) => json.encode(data.toJson());

@JsonSerializable()
class UpdateCityRequest {
  @JsonKey(name: "state_id")
  int? stateId;
  @J<PERSON><PERSON><PERSON>(name: "city_id")
  int? cityId;
  @<PERSON><PERSON><PERSON><PERSON>(name: "city_name")
  String? cityName;
  @JsonKey(name: "city_code")
  String? cityCode;
  @JsonKey(name: "status")
  String? status;

  UpdateCityRequest({
    this.stateId,
    this.cityId,
    this.cityName,
    this.cityCode,
    this.status,
  });

  factory UpdateCityRequest.fromJson(Map<String, dynamic> json) => _$UpdateCityRequestFromJson(json);

  Map<String, dynamic> toJson() => _$UpdateCityRequestToJson(this);
}
