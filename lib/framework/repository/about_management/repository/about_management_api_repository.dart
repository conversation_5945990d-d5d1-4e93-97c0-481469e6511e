import 'package:ds_admin/framework/repository/common_message_response.dart';
import 'package:ds_admin/framework/repository/rules_management/model/get_rules_response.dart';
import 'package:injectable/injectable.dart';

import '../../../../ui/utils/const/app_constants.dart';
import '../../../provider/network/network.dart';
import '../contract/about_management_repository.dart';
import '../model/update_about_request.dart';

@LazySingleton(as: AboutManagementRepository, env: [development, production])
class AboutManagementApiRepository extends AboutManagementRepository{
  DioClient apiClient;
  AboutManagementApiRepository(this.apiClient);


  @override
  Future getAbouts() async {
    try{
      Response? response = await apiClient.getRequest(ApiEndPoints.getAboutUs);
      GetRulesResponse responseModel = getRulesResponseFromJson(response.toString());
      if (response?.statusCode == ApiEndPoints.apiStatus_200) {
        return ApiResult.success(data: responseModel);
      } else {
        return ApiResult.failure(error: NetworkExceptions.defaultError(response?.statusMessage??''));
      }
    }catch(err){
      return ApiResult.failure(error: NetworkExceptions.getDioException(err));
    }
  }

  @override
  Future updateAbout(UpdateAboutRequest request) async {
    try{
      Map<String, dynamic> data = request.toJson();
      Response? response = await apiClient.putRequest(ApiEndPoints.updateAboutUs, data);
      CommonMessageResponse responseModel = commonMessageResponseFromJson(response.toString());
      if (response?.statusCode == ApiEndPoints.apiStatus_200) {
        return ApiResult.success(data: responseModel);
      } else {
        return ApiResult.failure(error: NetworkExceptions.defaultError(response?.statusMessage??''));
      }
    }catch(err){
      return ApiResult.failure(error: NetworkExceptions.getDioException(err));
    }
  }

}