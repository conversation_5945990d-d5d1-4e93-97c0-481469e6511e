// To parse this JSON data, do
//
//     final updateAboutRequest = updateAboutRequestFromJson(jsonString);

import 'package:json_annotation/json_annotation.dart';
import 'dart:convert';

part 'update_about_request.g.dart';

UpdateAboutRequest updateAboutRequestFromJson(String str) => UpdateAboutRequest.fromJson(json.decode(str));

String updateAboutRequestToJson(UpdateAboutRequest data) => json.encode(data.toJson());

@JsonSerializable()
class UpdateAboutRequest {
  @JsonKey(name: "about_id")
  int? aboutId;
  @Json<PERSON>ey(name: "title")
  String? title;
  @<PERSON><PERSON><PERSON><PERSON>(name: "description")
  String? description;

  UpdateAboutRequest({
    this.aboutId,
    this.title,
    this.description,
  });

  factory UpdateAboutRequest.fromJson(Map<String, dynamic> json) => _$UpdateAboutRequestFromJson(json);

  Map<String, dynamic> toJson() => _$UpdateAboutRequestToJson(this);
}
