import 'package:ds_admin/framework/provider/network/dio/dio_client.dart';
import 'package:ds_admin/framework/repository/common_message_response.dart';
import 'package:ds_admin/framework/repository/rules_management/contract/rules_management_repository.dart';
import 'package:ds_admin/framework/repository/rules_management/model/get_rules_response.dart';
import 'package:ds_admin/framework/repository/rules_management/model/update_rules_request.dart';
import 'package:injectable/injectable.dart';

import '../../../../ui/utils/const/app_constants.dart';
import '../../../provider/network/network.dart';

@LazySingleton(as: RulesManagementRepository, env: [development, production])
class RulesManagementApiRepository extends RulesManagementRepository{
  DioClient apiClient;
  RulesManagementApiRepository(this.apiClient);


  @override
  Future getRules() async {
    try{
      Response? response = await apiClient.getRequest(ApiEndPoints.getRules);
      GetRulesResponse responseModel = getRulesResponseFromJson(response.toString());
      if (response?.statusCode == ApiEndPoints.apiStatus_200) {
        return ApiResult.success(data: responseModel);
      } else {
        return ApiResult.failure(error: NetworkExceptions.defaultError(response?.statusMessage??''));
      }
    }catch(err){
      return ApiResult.failure(error: NetworkExceptions.getDioException(err));
    }
  }

  @override
  Future updateRule(UpdateRulesRequest request) async {
    try{
      Map<String, dynamic> data = request.toJson();
      Response? response = await apiClient.putRequest(ApiEndPoints.updateRules, data);
      CommonMessageResponse responseModel = commonMessageResponseFromJson(response.toString());
      if (response?.statusCode == ApiEndPoints.apiStatus_200) {
        return ApiResult.success(data: responseModel);
      } else {
        return ApiResult.failure(error: NetworkExceptions.defaultError(response?.statusMessage??''));
      }
    }catch(err){
      return ApiResult.failure(error: NetworkExceptions.getDioException(err));
    }
  }

}