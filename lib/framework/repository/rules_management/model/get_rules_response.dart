// To parse this JSON data, do
//
//     final getRulesResponse = getRulesResponseFromJson(jsonString);

import 'package:json_annotation/json_annotation.dart';
import 'dart:convert';

part 'get_rules_response.g.dart';

GetRulesResponse getRulesResponseFromJson(String str) => GetRulesResponse.fromJson(json.decode(str));

String getRulesResponseToJson(GetRulesResponse data) => json.encode(data.toJson());

@JsonSerializable()
class GetRulesResponse {
  @JsonKey(name: "status")
  bool? status;
  @Json<PERSON>ey(name: "message")
  String? message;
  @Json<PERSON>ey(name: "data")
  Data? data;

  GetRulesResponse({
    this.status,
    this.message,
    this.data,
  });

  factory GetRulesResponse.fromJson(Map<String, dynamic> json) => _$GetRulesResponseFromJson(json);

  Map<String, dynamic> toJson() => _$GetRulesResponseToJson(this);
}

@JsonSerializable()
class Data {
  @Json<PERSON>ey(name: "id")
  int? id;
  @Json<PERSON>ey(name: "title")
  String? title;
  @JsonKey(name: "description")
  String? description;

  Data({
    this.id,
    this.title,
    this.description,
  });

  factory Data.fromJson(Map<String, dynamic> json) => _$DataFromJson(json);

  Map<String, dynamic> toJson() => _$DataToJson(this);
}
