// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'get_rules_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GetRulesResponse _$GetRulesResponseFromJson(Map<String, dynamic> json) =>
    GetRulesResponse(
      status: json['status'] as bool?,
      message: json['message'] as String?,
      data: json['data'] == null
          ? null
          : Data.fromJson(json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$GetRulesResponseToJson(GetRulesResponse instance) =>
    <String, dynamic>{
      'status': instance.status,
      'message': instance.message,
      'data': instance.data,
    };

Data _$DataFromJson(Map<String, dynamic> json) => Data(
      id: (json['id'] as num?)?.toInt(),
      title: json['title'] as String?,
      description: json['description'] as String?,
    );

Map<String, dynamic> _$DataToJson(Data instance) => <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
    };
