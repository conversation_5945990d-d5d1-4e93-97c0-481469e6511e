// To parse this JSON data, do
//
//     final updateRulesRequest = updateRulesRequestFromJson(jsonString);

import 'package:json_annotation/json_annotation.dart';
import 'dart:convert';

part 'update_rules_request.g.dart';

UpdateRulesRequest updateRulesRequestFromJson(String str) => UpdateRulesRequest.fromJson(json.decode(str));

String updateRulesRequestToJson(UpdateRulesRequest data) => json.encode(data.toJson());

@JsonSerializable()
class UpdateRulesRequest {
  @Json<PERSON>ey(name: "rule_id")
  int? ruleId;
  @J<PERSON><PERSON><PERSON>(name: "title")
  String? title;
  @J<PERSON><PERSON><PERSON>(name: "description")
  String? description;

  UpdateRulesRequest({
    this.ruleId,
    this.title,
    this.description,
  });

  factory UpdateRulesRequest.fromJson(Map<String, dynamic> json) => _$UpdateRulesRequestFromJson(json);

  Map<String, dynamic> toJson() => _$UpdateRulesRequestToJson(this);
}
