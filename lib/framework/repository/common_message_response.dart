// To parse this JSON data, do
//
//     final commonMessageResponse = commonMessageResponseFromJson(jsonString);

import 'package:json_annotation/json_annotation.dart';
import 'dart:convert';

part 'common_message_response.g.dart';

CommonMessageResponse commonMessageResponseFromJson(String str) => CommonMessageResponse.fromJson(json.decode(str));

String commonMessageResponseToJson(CommonMessageResponse data) => json.encode(data.toJson());

@JsonSerializable()
class CommonMessageResponse {
  @JsonKey(name: "status")
  bool? status;
  @JsonKey(name: "message")
  String? message;

  CommonMessageResponse({
    this.status,
    this.message,
  });

  factory CommonMessageResponse.fromJson(Map<String, dynamic> json) => _$CommonMessageResponseFromJson(json);

  Map<String, dynamic> toJson() => _$CommonMessageResponseToJson(this);
}
