import 'package:ds_admin/framework/repository/dashboard/contract/dashboard_repository.dart';
import 'package:ds_admin/framework/repository/dashboard/model/dashboard_response.dart';
import 'package:ds_admin/ui/utils/const/app_constants.dart';
import 'package:injectable/injectable.dart';

import '../../../provider/network/network.dart';

@LazySingleton(as: DashboardRepository, env: [development, production])
class DashboardApiRepository extends DashboardRepository{
  final DioClient apiClient;
  DashboardApiRepository(this.apiClient);

  @override
  Future getDashboard() async {
    try {
      Response? response = await apiClient.getRequest(ApiEndPoints.getDashboardData, isTokenRequiredInHeader: true);
      DashboardResponse responseModel = dashboardResponseFromJson(response.toString());
      if (response?.statusCode == ApiEndPoints.apiStatus_200) {
        return ApiResult.success(data: responseModel);
      } else {
        return ApiResult.failure(error: NetworkExceptions.defaultError(response?.statusMessage ?? ''));
      }
    } catch (err) {
      return ApiResult.failure(error: NetworkExceptions.getDioException(err));
    }
  }

}