// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'dashboard_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DashboardResponse _$DashboardResponseFromJson(Map<String, dynamic> json) =>
    DashboardResponse(
      status: json['status'] as bool?,
      message: json['message'] as String?,
      data: json['data'] == null
          ? null
          : Data.fromJson(json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$DashboardResponseT<PERSON>Json(DashboardResponse instance) =>
    <String, dynamic>{
      'status': instance.status,
      'message': instance.message,
      'data': instance.data,
    };

Data _$DataFromJson(Map<String, dynamic> json) => Data(
      totalMembers: (json['total_members'] as num?)?.toInt(),
      totalActiveMaleMembers:
          (json['total_active_male_members'] as num?)?.toInt(),
      totalActiveFemaleMembers:
          (json['total_active_female_members'] as num?)?.toInt(),
      totalCities: (json['total_cities'] as num?)?.toInt(),
      totalStates: (json['total_states'] as num?)?.toInt(),
      ageWiseChart: (json['age_wise_chart'] as List<dynamic>?)
          ?.map((e) => AgeWiseChartData.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$DataToJson(Data instance) => <String, dynamic>{
      'total_members': instance.totalMembers,
      'total_active_male_members': instance.totalActiveMaleMembers,
      'total_active_female_members': instance.totalActiveFemaleMembers,
      'total_cities': instance.totalCities,
      'total_states': instance.totalStates,
      'age_wise_chart': instance.ageWiseChart,
    };

AgeWiseChartData _$AgeWiseChartDataFromJson(Map<String, dynamic> json) =>
    AgeWiseChartData(
      ageGroup: json['age_group'] as String?,
      percentage: json['percentage'] as String?,
    );

Map<String, dynamic> _$AgeWiseChartDataToJson(AgeWiseChartData instance) =>
    <String, dynamic>{
      'age_group': instance.ageGroup,
      'percentage': instance.percentage,
    };
