// To parse this JSON data, do
//
//     final dashboardResponse = dashboardResponseFromJson(jsonString);

import 'package:json_annotation/json_annotation.dart';
import 'dart:convert';

part 'dashboard_response.g.dart';

DashboardResponse dashboardResponseFromJson(String str) => DashboardResponse.fromJson(json.decode(str));

String dashboardResponseToJson(DashboardResponse data) => json.encode(data.toJson());

@JsonSerializable()
class DashboardResponse {
  @JsonKey(name: "status")
  bool? status;
  @JsonKey(name: "message")
  String? message;
  @Json<PERSON>ey(name: "data")
  Data? data;

  DashboardResponse({
    this.status,
    this.message,
    this.data,
  });

  factory DashboardResponse.fromJson(Map<String, dynamic> json) => _$DashboardResponseFromJson(json);

  Map<String, dynamic> toJson() => _$DashboardResponseToJson(this);
}

@JsonSerializable()
class Data {
  @JsonKey(name: "total_members")
  int? totalMembers;
  @JsonKey(name: "total_active_male_members")
  int? totalActiveMaleMembers;
  @JsonKey(name: "total_active_female_members")
  int? totalActiveFemaleMembers;
  @JsonKey(name: "total_cities")
  int? totalCities;
  @JsonKey(name: "total_states")
  int? totalStates;
  @JsonKey(name: "age_wise_chart")
  List<AgeWiseChartData>? ageWiseChart;

  Data({
    this.totalMembers,
    this.totalActiveMaleMembers,
    this.totalActiveFemaleMembers,
    this.totalCities,
    this.totalStates,
    this.ageWiseChart,
  });

  factory Data.fromJson(Map<String, dynamic> json) => _$DataFromJson(json);

  Map<String, dynamic> toJson() => _$DataToJson(this);
}

@JsonSerializable()
class AgeWiseChartData {
  @JsonKey(name: "age_group")
  String? ageGroup;
  @JsonKey(name: "percentage")
  String? percentage;

  AgeWiseChartData({
    this.ageGroup,
    this.percentage,
  });

  factory AgeWiseChartData.fromJson(Map<String, dynamic> json) => _$AgeWiseChartDataFromJson(json);

  Map<String, dynamic> toJson() => _$AgeWiseChartDataToJson(this);
}
