// To parse this JSON data, do
//
//     final getStatesResponse = getStatesResponseFromJson(jsonString);

import 'package:json_annotation/json_annotation.dart';
import 'dart:convert';

part 'get_states_response.g.dart';

GetStatesResponse getStatesResponseFromJson(String str) => GetStatesResponse.fromJson(json.decode(str));

String getStatesResponseToJson(GetStatesResponse data) => json.encode(data.toJson());

@JsonSerializable()
class GetStatesResponse {
  @JsonKey(name: "status")
  bool? status;
  @JsonKey(name: "message")
  String? message;
  @Json<PERSON>ey(name: "data")
  Data? data;

  GetStatesResponse({
    this.status,
    this.message,
    this.data,
  });

  factory GetStatesResponse.fromJson(Map<String, dynamic> json) => _$GetStatesResponseFromJson(json);

  Map<String, dynamic> toJson() => _$GetStatesResponseToJson(this);
}

@JsonSerializable()
class Data {
  @JsonKey(name: "states")
  List<States>? states;

  Data({
    this.states,
  });

  factory Data.fromJson(Map<String, dynamic> json) => _$DataFromJson(json);

  Map<String, dynamic> toJson() => _$DataToJson(this);
}

@JsonSerializable()
class States {
  @JsonKey(name: "state_id")
  int? stateId;
  @JsonKey(name: "state_name")
  String? stateName;
  @JsonKey(name: "state_code")
  String? stateCode;
  @JsonKey(name: "status")
  String? status;

  States({
    this.stateId,
    this.stateName,
    this.stateCode,
    this.status,
  });

  factory States.fromJson(Map<String, dynamic> json) => _$StatesFromJson(json);

  Map<String, dynamic> toJson() => _$StatesToJson(this);
}
