// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'get_states_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GetStatesResponse _$GetStatesResponseFromJson(Map<String, dynamic> json) =>
    GetStatesResponse(
      status: json['status'] as bool?,
      message: json['message'] as String?,
      data: json['data'] == null
          ? null
          : Data.fromJson(json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$GetStatesResponseToJson(GetStatesResponse instance) =>
    <String, dynamic>{
      'status': instance.status,
      'message': instance.message,
      'data': instance.data,
    };

Data _$DataFromJson(Map<String, dynamic> json) => Data(
      states: (json['states'] as List<dynamic>?)
          ?.map((e) => States.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$DataToJson(Data instance) => <String, dynamic>{
      'states': instance.states,
    };

States _$StatesFromJson(Map<String, dynamic> json) => States(
      stateId: (json['state_id'] as num?)?.toInt(),
      stateName: json['state_name'] as String?,
      stateCode: json['state_code'] as String?,
      status: json['status'] as String?,
    );

Map<String, dynamic> _$StatesToJson(States instance) => <String, dynamic>{
      'state_id': instance.stateId,
      'state_name': instance.stateName,
      'state_code': instance.stateCode,
      'status': instance.status,
    };
