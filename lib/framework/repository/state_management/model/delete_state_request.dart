// To parse this JSON data, do
//
//     final deleteStateRequest = deleteStateRequestFromJson(jsonString);

import 'package:json_annotation/json_annotation.dart';
import 'dart:convert';

part 'delete_state_request.g.dart';

DeleteStateRequest deleteStateRequestFromJson(String str) => DeleteStateRequest.fromJson(json.decode(str));

String deleteStateRequestToJson(DeleteStateRequest data) => json.encode(data.toJson());

@JsonSerializable()
class DeleteStateRequest {
  @JsonKey(name: "state_id")
  String? stateId;

  DeleteStateRequest({
    this.stateId,
  });

  factory DeleteStateRequest.fromJson(Map<String, dynamic> json) => _$DeleteStateRequestFromJson(json);

  Map<String, dynamic> toJson() => _$DeleteStateRequestToJson(this);
}
