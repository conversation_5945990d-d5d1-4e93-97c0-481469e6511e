// To parse this JSON data, do
//
//     final updateStateRequest = updateStateRequestFromJson(jsonString);

import 'package:json_annotation/json_annotation.dart';
import 'dart:convert';

part 'update_state_request.g.dart';

UpdateStateRequest updateStateRequestFromJson(String str) => UpdateStateRequest.fromJson(json.decode(str));

String updateStateRequestToJson(UpdateStateRequest data) => json.encode(data.toJson());

@JsonSerializable()
class UpdateStateRequest {
  @Json<PERSON>ey(name: "state_id")
  String? stateId;
  @<PERSON><PERSON><PERSON><PERSON>(name: "state_name")
  String? stateName;
  @J<PERSON><PERSON>ey(name: "state_code")
  String? stateCode;
  @JsonKey(name: "status")
  String? status;

  UpdateStateRequest({
    this.stateId,
    this.stateName,
    this.stateCode,
    this.status,
  });

  factory UpdateStateRequest.fromJson(Map<String, dynamic> json) => _$UpdateStateRequestFromJson(json);

  Map<String, dynamic> toJson() => _$UpdateStateRequestToJson(this);
}
