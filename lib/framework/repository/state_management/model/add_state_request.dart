// To parse this JSON data, do
//
//     final addStateRequest = addStateRequestFromJson(jsonString);

import 'package:json_annotation/json_annotation.dart';
import 'dart:convert';

part 'add_state_request.g.dart';

AddStateRequest addStateRequestFromJson(String str) => AddStateRequest.fromJson(json.decode(str));

String addStateRequestToJson(AddStateRequest data) => json.encode(data.toJson());

@JsonSerializable()
class AddStateRequest {
  @Json<PERSON>ey(name: "state_name")
  String? stateName;
  @J<PERSON><PERSON><PERSON>(name: "state_code")
  String? stateCode;
  @Json<PERSON>ey(name: "status")
  String? status;

  AddStateRequest({
    this.stateName,
    this.stateCode,
    this.status,
  });

  factory AddStateRequest.fromJson(Map<String, dynamic> json) => _$AddStateRequestFromJson(json);

  Map<String, dynamic> toJson() => _$AddStateRequestToJson(this);
}
