import 'package:ds_admin/framework/repository/state_management/contract/state_management_repository.dart';
import 'package:ds_admin/framework/repository/state_management/model/add_state_request.dart';
import 'package:ds_admin/framework/repository/state_management/model/delete_state_request.dart';
import 'package:ds_admin/framework/repository/state_management/model/get_states_response.dart';
import 'package:ds_admin/framework/repository/state_management/model/update_state_request.dart';
import 'package:injectable/injectable.dart';

import '../../../../ui/utils/const/app_constants.dart';
import '../../../provider/network/network.dart';
import '../../common_message_response.dart';

@LazySingleton(as: StateManagementRepository, env: [development, production])
class StateManagementApiRepository extends StateManagementRepository{

  final DioClient apiClient;
  StateManagementApiRepository(this.apiClient);

  @override
  Future addState(AddStateRequest request) async{
    try{
      Response? response = await apiClient.postRequest(ApiEndPoints.addState, request.toJson(), isTokenRequiredInHeader: true);
      CommonMessageResponse responseModel = commonMessageResponseFromJson(response.toString());
      if (response?.statusCode == ApiEndPoints.apiStatus_200) {
        return ApiResult.success(data: responseModel);
      } else {
        return ApiResult.failure(error: NetworkExceptions.defaultError(response?.statusMessage ?? ''));
      }
    } catch (err) {
      return ApiResult.failure(error: NetworkExceptions.getDioException(err));
    }
  }

  @override
  Future deleteState(int stateId) async{
    DeleteStateRequest request = DeleteStateRequest(stateId: stateId.toString());
    try{
      Response? response = await apiClient.deleteRequest(ApiEndPoints.deleteState, request.toJson(), isTokenRequiredInHeader: true);
      CommonMessageResponse responseModel = commonMessageResponseFromJson(response.toString());
      if (response?.statusCode == ApiEndPoints.apiStatus_200) {
        return ApiResult.success(data: responseModel);
      } else {
        return ApiResult.failure(error: NetworkExceptions.defaultError(response?.statusMessage ?? ''));
      }
    } catch (err) {
      return ApiResult.failure(error: NetworkExceptions.getDioException(err));
    }
  }

  @override
  Future getStates() async {
    try {
      Response? response = await apiClient.getRequest(ApiEndPoints.getStates, isTokenRequiredInHeader: true);
      GetStatesResponse responseModel = getStatesResponseFromJson(response.toString());
      if (response?.statusCode == ApiEndPoints.apiStatus_200) {
        return ApiResult.success(data: responseModel);
      } else {
        return ApiResult.failure(error: NetworkExceptions.defaultError(response?.statusMessage ?? ''));
      }
    } catch (err) {
      return ApiResult.failure(error: NetworkExceptions.getDioException(err));
    }
  }

  @override
  Future updateState(UpdateStateRequest request) async {
    try{
      Response? response = await apiClient.putRequest(ApiEndPoints.updateState, request.toJson(), isTokenRequiredInHeader: true);
      CommonMessageResponse responseModel = commonMessageResponseFromJson(response.toString());
      if (response?.statusCode == ApiEndPoints.apiStatus_200) {
        return ApiResult.success(data: responseModel);
      } else {
        return ApiResult.failure(error: NetworkExceptions.defaultError(response?.statusMessage ?? ''));
      }
    } catch (err) {
      return ApiResult.failure(error: NetworkExceptions.getDioException(err));
    }
  }
}