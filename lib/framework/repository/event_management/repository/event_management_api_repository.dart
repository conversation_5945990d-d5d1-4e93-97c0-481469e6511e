import 'package:ds_admin/framework/repository/common_message_response.dart';
import 'package:ds_admin/framework/repository/event_management/contract/event_management_repository.dart';
import 'package:ds_admin/framework/repository/event_management/model/add_event_request.dart';
import 'package:ds_admin/framework/repository/event_management/model/delete_event_request.dart';
import 'package:ds_admin/framework/repository/event_management/model/edit_event_request.dart';
import 'package:flutter/foundation.dart';
import 'package:injectable/injectable.dart';

import '../../../../ui/utils/const/app_constants.dart';
import '../../../provider/network/network.dart';
import '../model/get_events_response.dart';

@LazySingleton(as: EventManagementRepository, env: [development, production])
class EventManagementApiRepository extends EventManagementRepository {
  final DioClient apiClient;
  EventManagementApiRepository(this.apiClient);

  @override
  Future addEvent(AddEventRequest event, Uint8List? eventPhoto) async {
    try {
      FormData formData = FormData.fromMap(event.toJson());

      if (eventPhoto != null) {
        formData.files.add(MapEntry(
          'event_photo',
          MultipartFile.fromBytes(eventPhoto, filename: 'event_photo_${DateTime.now().millisecondsSinceEpoch}.png'),
        ));
      }

      Response? response = eventPhoto != null
          ? await apiClient.postRequestFormData(ApiEndPoints.addEvent, formData, isTokenRequiredInHeader: true)
          : await apiClient.postRequest(ApiEndPoints.addEvent, event.toJson(), isTokenRequiredInHeader: true);
      if (kDebugMode) {
        print("Add event response: ${response.toString()}");
      }
      CommonMessageResponse responseModel = commonMessageResponseFromJson(response.toString());
      if (response?.statusCode == ApiEndPoints.apiStatus_200) {
        return ApiResult.success(data: responseModel);
      } else {
        return ApiResult.failure(error: NetworkExceptions.defaultError(response?.statusMessage ?? ''));
      }
    } catch (err) {
      return ApiResult.failure(error: NetworkExceptions.getDioException(err));
    }
  }

  @override
  Future deleteEvent(int eventId) async {
    DeleteEventRequest request = DeleteEventRequest(eventId: eventId);
    try{
      Response? response = await apiClient.deleteRequest(ApiEndPoints.deleteEvent, request.toJson(), isTokenRequiredInHeader: true);
      CommonMessageResponse responseModel = commonMessageResponseFromJson(response.toString());
      if (response?.statusCode == ApiEndPoints.apiStatus_200) {
        return ApiResult.success(data: responseModel);
      } else {
        return ApiResult.failure(error: NetworkExceptions.defaultError(response?.statusMessage ?? ''));
      }
    } catch (err) {
      return ApiResult.failure(error: NetworkExceptions.getDioException(err));
    }
  }

  @override
  Future getEvents(int page, String searchText) async {
    try {
      Response? response = await apiClient.getRequest(ApiEndPoints.getEvents(page, searchText), isTokenRequiredInHeader: true);
      GetEventsResponse responseModel = getEventsResponseFromJson(response.toString());
      if (response?.statusCode == ApiEndPoints.apiStatus_200) {
        return ApiResult.success(data: responseModel);
      } else {
        return ApiResult.failure(error: NetworkExceptions.defaultError(response?.statusMessage ?? ''));
      }
    } catch (err) {
      return ApiResult.failure(error: NetworkExceptions.getDioException(err));
    }
  }

  @override
  Future updateEvent(EditEventRequest event, Uint8List? eventPhoto) async {
    try {
      FormData formData = FormData.fromMap(event.toJson());
      if (eventPhoto != null) {
        formData.files.add(MapEntry(
          'event_photo',
          MultipartFile.fromBytes(eventPhoto, filename: 'event_photo_${DateTime.now().millisecondsSinceEpoch}.png'),
        ));
      }

      Response? response = eventPhoto != null
          ? await apiClient.putRequestFormData(ApiEndPoints.updateEvent, formData, isTokenRequiredInHeader: true)
          : await apiClient.putRequest(ApiEndPoints.updateEvent, event.toJson(), isTokenRequiredInHeader: true);
      CommonMessageResponse responseModel = commonMessageResponseFromJson(response.toString());
      if (response?.statusCode == ApiEndPoints.apiStatus_200) {
        return ApiResult.success(data: responseModel);
      } else {
        return ApiResult.failure(error: NetworkExceptions.defaultError(response?.statusMessage ?? ''));
      }
    } catch (err) {
      return ApiResult.failure(error: NetworkExceptions.getDioException(err));
    }
  }
}
