// To parse this JSON data, do
//
//     final editEventRequest = editEventRequestFromJson(jsonString);

import 'package:json_annotation/json_annotation.dart';
import 'dart:convert';

part 'edit_event_request.g.dart';

EditEventRequest editEventRequestFromJson(String str) => EditEventRequest.fromJson(json.decode(str));

String editEventRequestToJson(EditEventRequest data) => json.encode(data.toJson());

@JsonSerializable()
class EditEventRequest {
  @Json<PERSON>ey(name: "event_title")
  String? eventTitle;
  @<PERSON><PERSON><PERSON><PERSON>(name: "event_id")
  int? eventId;
  @J<PERSON><PERSON><PERSON>(name: "event_date")
  String? eventDate;
  @JsonKey(name: "event_description")
  String? eventDescription;

  EditEventRequest({
    this.eventTitle,
    this.eventId,
    this.eventDate,
    this.eventDescription,
  });

  factory EditEventRequest.fromJson(Map<String, dynamic> json) => _$EditEventRequestFromJson(json);

  Map<String, dynamic> toJson() => _$EditEventRequestToJson(this);
}
