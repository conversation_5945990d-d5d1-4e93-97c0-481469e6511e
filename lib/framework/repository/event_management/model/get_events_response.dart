// To parse this JSON data, do
//
//     final getEventsResponse = getEventsResponseFromJson(jsonString);

import 'package:json_annotation/json_annotation.dart';
import 'dart:convert';

part 'get_events_response.g.dart';

GetEventsResponse getEventsResponseFromJson(String str) => GetEventsResponse.fromJson(json.decode(str));

String getEventsResponseToJson(GetEventsResponse data) => json.encode(data.toJson());

@JsonSerializable()
class GetEventsResponse {
  @JsonKey(name: "status")
  bool? status;
  @JsonKey(name: "message")
  String? message;
  @Json<PERSON>ey(name: "data")
  Data? data;

  GetEventsResponse({
    this.status,
    this.message,
    this.data,
  });

  factory GetEventsResponse.fromJson(Map<String, dynamic> json) => _$GetEventsResponseFromJson(json);

  Map<String, dynamic> toJson() => _$GetEventsResponseToJson(this);
}

@JsonSerializable()
class Data {
  @JsonKey(name: "current_page")
  int? currentPage;
  @JsonKey(name: "per_page")
  int? perPage;
  @JsonKey(name: "total_pages")
  int? totalPages;
  @JsonKey(name: "total_items")
  int? totalItems;
  @JsonKey(name: "events")
  List<Event>? events;

  Data({
    this.currentPage,
    this.perPage,
    this.totalPages,
    this.totalItems,
    this.events,
  });

  factory Data.fromJson(Map<String, dynamic> json) => _$DataFromJson(json);

  Map<String, dynamic> toJson() => _$DataToJson(this);
}

@JsonSerializable()
class Event {
  @JsonKey(name: "event_id")
  int? eventId;
  @JsonKey(name: "event_title")
  String? eventTitle;
  @JsonKey(name: "event_date")
  String? eventDate;
  @JsonKey(name: "event_description")
  String? eventDescription;
  @JsonKey(name: "event_photo")
  String? eventPhoto;

  Event({
    this.eventId,
    this.eventTitle,
    this.eventDate,
    this.eventDescription,
    this.eventPhoto,
  });

  factory Event.fromJson(Map<String, dynamic> json) => _$EventFromJson(json);

  Map<String, dynamic> toJson() => _$EventToJson(this);
}
