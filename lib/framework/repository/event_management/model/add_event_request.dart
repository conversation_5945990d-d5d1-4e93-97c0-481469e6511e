// To parse this JSON data, do
//
//     final addEventRequest = addEventRequestFromJson(jsonString);

import 'package:json_annotation/json_annotation.dart';
import 'dart:convert';

part 'add_event_request.g.dart';

AddEventRequest addEventRequestFromJson(String str) => AddEventRequest.fromJson(json.decode(str));

String addEventRequestToJson(AddEventRequest data) => json.encode(data.toJson());

@JsonSerializable()
class AddEventRequest {
  @JsonKey(name: "event_title")
  String? eventTitle;
  @J<PERSON><PERSON>ey(name: "event_date")
  String? eventDate;
  @J<PERSON><PERSON><PERSON>(name: "event_description")
  String? eventDescription;

  AddEventRequest({
    this.eventTitle,
    this.eventDate,
    this.eventDescription,
  });

  factory AddEventRequest.fromJson(Map<String, dynamic> json) => _$AddEventRequestFromJson(json);

  Map<String, dynamic> toJson() => _$AddEventRequestToJson(this);
}
