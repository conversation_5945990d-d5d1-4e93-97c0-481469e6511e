// To parse this JSON data, do
//
//     final deleteEventRequest = deleteEventRequestFromJson(jsonString);

import 'package:json_annotation/json_annotation.dart';
import 'dart:convert';

part 'delete_event_request.g.dart';

DeleteEventRequest deleteEventRequestFromJson(String str) => DeleteEventRequest.fromJson(json.decode(str));

String deleteEventRequestToJson(DeleteEventRequest data) => json.encode(data.toJson());

@JsonSerializable()
class DeleteEventRequest {
  @JsonKey(name: "event_id")
  int? eventId;

  DeleteEventRequest({
    this.eventId,
  });

  factory DeleteEventRequest.fromJson(Map<String, dynamic> json) => _$DeleteEventRequestFromJson(json);

  Map<String, dynamic> toJson() => _$DeleteEventRequestToJson(this);
}
