import 'dart:typed_data';

import 'package:ds_admin/framework/repository/event_management/model/add_event_request.dart';
import 'package:ds_admin/framework/repository/event_management/model/edit_event_request.dart';

abstract class EventManagementRepository {
  Future getEvents(int page, String searchText);
  Future addEvent(AddEventRequest event, Uint8List? eventPhoto);
  Future updateEvent(EditEventRequest event, Uint8List? eventPhoto);
  Future deleteEvent(int eventId);
}