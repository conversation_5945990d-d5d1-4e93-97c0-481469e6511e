// To parse this JSON data, do
//
//     final adminLoginResponse = adminLoginResponseFromJson(jsonString);

import 'package:json_annotation/json_annotation.dart';
import 'dart:convert';

part 'admin_login_response.g.dart';

AdminLoginResponse adminLoginResponseFromJson(String str) => AdminLoginResponse.fromJson(json.decode(str));

String adminLoginResponseToJson(AdminLoginResponse data) => json.encode(data.toJson());

@JsonSerializable()
class AdminLoginResponse {
  @JsonKey(name: "status")
  bool status;
  @JsonKey(name: "message")
  String message;
  @JsonKey(name: "data")
  Data data;

  AdminLoginResponse({
    required this.status,
    required this.message,
    required this.data,
  });

  factory AdminLoginResponse.fromJson(Map<String, dynamic> json) => _$AdminLoginResponseFromJson(json);

  Map<String, dynamic> toJson() => _$AdminLoginResponseToJson(this);
}

@JsonSerializable()
class Data {
  @JsonKey(name: "id")
  int id;
  @JsonKey(name: "token")
  String token;

  Data({
    required this.id,
    required this.token,
  });

  factory Data.fromJson(Map<String, dynamic> json) => _$DataFromJson(json);

  Map<String, dynamic> toJson() => _$DataToJson(this);
}
