// To parse this JSON data, do
//
//     final adminLoginRequest = adminLoginRequestFromJson(jsonString);

import 'package:json_annotation/json_annotation.dart';
import 'dart:convert';

part 'admin_login_request.g.dart';

AdminLoginRequest adminLoginRequestFromJson(String str) => AdminLoginRequest.fromJson(json.decode(str));

String adminLoginRequestToJson(AdminLoginRequest data) => json.encode(data.toJson());

@JsonSerializable()
class AdminLoginRequest {
  @Json<PERSON>ey(name: "username")
  String username;
  @Json<PERSON>ey(name: "password")
  String password;

  AdminLoginRequest({
    required this.username,
    required this.password,
  });

  factory AdminLoginRequest.fromJson(Map<String, dynamic> json) => _$AdminLoginRequestFromJson(json);

  Map<String, dynamic> toJson() => _$AdminLoginRequestToJson(this);
}
