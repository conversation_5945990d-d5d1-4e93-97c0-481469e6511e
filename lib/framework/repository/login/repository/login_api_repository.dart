import 'package:ds_admin/framework/repository/login/contract/login_repository.dart';
import 'package:ds_admin/framework/repository/login/model/admin_login_request.dart';
import 'package:ds_admin/ui/utils/const/app_constants.dart';
import 'package:injectable/injectable.dart';
import '../../../provider/network/network.dart';
import '../model/admin_login_response.dart';

@LazySingleton(as: LoginRepository, env: [development, production])
class LoginApiRepository implements LoginRepository{

  final DioClient apiClient;
  LoginApiRepository(this.apiClient);

  @override
  Future adminLoginApi(AdminLoginRequest request) async {
    try {
      // String data = json.encode(request.toJson());
      Map<String, dynamic> data = request.toJson();
      Response? response = await apiClient.postRequest(ApiEndPoints.signIn, data, isTokenRequiredInHeader: false);
      AdminLoginResponse responseModel = adminLoginResponseFromJson(response.toString());
      if (response?.statusCode == ApiEndPoints.apiStatus_200) {
        return ApiResult.success(data: responseModel);
      } else {
        return ApiResult.failure(error: NetworkExceptions.defaultError(response?.statusMessage??''));
      }
    } catch (err) {
      return ApiResult.failure(error: NetworkExceptions.getDioException(err));
    }
  }

}