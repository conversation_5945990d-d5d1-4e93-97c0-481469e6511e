import 'package:ds_admin/framework/repository/common_message_response.dart';
import 'package:ds_admin/framework/repository/member_management/contract/member_management_repository.dart';
import 'package:ds_admin/framework/repository/member_management/model/add_member_request.dart';
import 'package:ds_admin/framework/repository/member_management/model/delete_member_request.dart';
import 'package:ds_admin/framework/repository/member_management/model/get_members_response.dart';
import 'package:ds_admin/framework/repository/member_management/model/update_member_request.dart';
import 'package:ds_admin/framework/repository/state_management/model/get_states_response.dart';
import 'package:injectable/injectable.dart';

import '../../../../ui/utils/const/app_constants.dart';
import '../../../provider/network/network.dart';
import '../../city_management/model/get_cities_response.dart';

@LazySingleton(as: MemberManagementRepository, env: [development, production])
class MemberManagementApiRepository extends MemberManagementRepository{
  final DioClient apiClient;
  MemberManagementApiRepository(this.apiClient);

  @override
  Future addMember(AddMemberRequest request) async {
    try{
      Response? response = await apiClient.postRequest(ApiEndPoints.addMember, request.toJson(), isTokenRequiredInHeader: true);
      CommonMessageResponse responseModel = commonMessageResponseFromJson(response.toString());
      if (response?.statusCode == ApiEndPoints.apiStatus_200) {
        return ApiResult.success(data: responseModel);
      } else {
        return ApiResult.failure(error: NetworkExceptions.defaultError(response?.statusMessage??''));
      }
    }catch(err){
      return ApiResult.failure(error: NetworkExceptions.getDioException(err));
    }
  }

  @override
  Future updateMember(UpdateMemberRequest request) async {
    try {
      Response? response = await apiClient.putRequest(ApiEndPoints.updateMember, request.toJson(), isTokenRequiredInHeader: true);
      CommonMessageResponse responseModel = commonMessageResponseFromJson(response.toString());
      if (response?.statusCode == ApiEndPoints.apiStatus_200) {
        return ApiResult.success(data: responseModel);
      } else {
        return ApiResult.failure(error: NetworkExceptions.defaultError(response?.statusMessage ?? ''));
      }
    }catch(err){
      return ApiResult.failure(error: NetworkExceptions.getDioException(err));
    }
  }

  @override
  Future getMembersList(int page, String language, String? memberName, String? cityId, int? minAge, int? maxAge, String? martialStatus) async {
    try {
      Response? response = await apiClient.getRequest(ApiEndPoints.getMembers(page, language, memberName, cityId, minAge, maxAge, martialStatus), isTokenRequiredInHeader: true);
      GetMembersResponse responseModel = getMembersResponseFromJson(response.toString());
      if (response?.statusCode == ApiEndPoints.apiStatus_200) {
        return ApiResult.success(data: responseModel);
      } else {
        return ApiResult.failure(error: NetworkExceptions.defaultError(response?.statusMessage??''));
      }
    } catch (err) {
      return ApiResult.failure(error: NetworkExceptions.getDioException(err));
    }
  }

  @override
  Future getCities(String? stateId) async {
    try {
      Response? response = await apiClient.getRequest(ApiEndPoints.getActiveCities(stateId ?? ''), isTokenRequiredInHeader: true);
      GetCitiesResponse responseModel = getCitiesResponseFromJson(response.toString());
      if (response?.statusCode == ApiEndPoints.apiStatus_200) {
        return ApiResult.success(data: responseModel);
      } else {
        return ApiResult.failure(error: NetworkExceptions.defaultError(response?.statusMessage??''));
      }
    } catch (err) {
      return ApiResult.failure(error: NetworkExceptions.getDioException(err));
    }
  }

  @override
  Future deleteMember(int memberId) async {
    DeleteMemberRequest request = DeleteMemberRequest(memberId: memberId);
    try {
      Response? response = await apiClient.deleteRequest(ApiEndPoints.deleteMember, request.toJson(), isTokenRequiredInHeader: true);
      CommonMessageResponse responseModel = commonMessageResponseFromJson(response.toString());
      if (response?.statusCode == ApiEndPoints.apiStatus_200) {
        return ApiResult.success(data: responseModel);
      } else {
        return ApiResult.failure(error: NetworkExceptions.defaultError(response?.statusMessage??''));
      }
    } catch (err) {
      return ApiResult.failure(error: NetworkExceptions.getDioException(err));
    }
  }

  @override
  Future getStates() async {
    try {
      Response? response = await apiClient.getRequest(ApiEndPoints.getActiveStates, isTokenRequiredInHeader: true);
      GetStatesResponse responseModel = getStatesResponseFromJson(response.toString());
      if (response?.statusCode == ApiEndPoints.apiStatus_200) {
        return ApiResult.success(data: responseModel);
      } else {
        return ApiResult.failure(error: NetworkExceptions.defaultError(response?.statusMessage??''));
      }
    } catch (err) {
      return ApiResult.failure(error: NetworkExceptions.getDioException(err));
    }
  }

}