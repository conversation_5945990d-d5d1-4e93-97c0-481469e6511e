// To parse this JSON data, do
//
//     final deleteMemberRequest = deleteMemberRequestFromJson(jsonString);

import 'package:json_annotation/json_annotation.dart';
import 'dart:convert';

part 'delete_member_request.g.dart';

DeleteMemberRequest deleteMemberRequestFromJson(String str) => DeleteMemberRequest.fromJson(json.decode(str));

String deleteMemberRequestToJson(DeleteMemberRequest data) => json.encode(data.toJson());

@JsonSerializable()
class DeleteMemberRequest {
  @JsonKey(name: "member_id")
  int? memberId;

  DeleteMemberRequest({
    this.memberId,
  });

  factory DeleteMemberRequest.fromJson(Map<String, dynamic> json) => _$DeleteMemberRequestFromJson(json);

  Map<String, dynamic> toJson() => _$DeleteMemberRequestToJson(this);
}
