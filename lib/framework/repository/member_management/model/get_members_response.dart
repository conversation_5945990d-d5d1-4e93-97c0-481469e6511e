// To parse this JSON data, do
//
//     final getMembersResponse = getMembersResponseFromJson(jsonString);

import 'package:json_annotation/json_annotation.dart';
import 'dart:convert';

part 'get_members_response.g.dart';

GetMembersResponse getMembersResponseFromJson(String str) => GetMembersResponse.fromJson(json.decode(str));

String getMembersResponseToJson(GetMembersResponse data) => json.encode(data.toJson());

@JsonSerializable()
class GetMembersResponse {
  @JsonKey(name: "status")
  bool? status;
  @JsonKey(name: "message")
  String? message;
  @Json<PERSON>ey(name: "data")
  Data? data;

  GetMembersResponse({
    this.status,
    this.message,
    this.data,
  });

  factory GetMembersResponse.fromJson(Map<String, dynamic> json) => _$GetMembersResponseFromJson(json);

  Map<String, dynamic> toJson() => _$GetMembersResponseToJson(this);
}

@JsonSerializable()
class Data {
  @JsonKey(name: "current_page")
  int? currentPage;
  @JsonKey(name: "per_page")
  int? perPage;
  @JsonKey(name: "total_pages")
  int? totalPages;
  @JsonKey(name: "total_items")
  int? totalItems;
  @JsonKey(name: "members")
  List<Member>? members;
  @JsonKey(name: "members_info")
  MembersInfo? membersInfo;

  Data({
    this.currentPage,
    this.perPage,
    this.totalPages,
    this.totalItems,
    this.members,
    this.membersInfo,
  });

  factory Data.fromJson(Map<String, dynamic> json) => _$DataFromJson(json);

  Map<String, dynamic> toJson() => _$DataToJson(this);
}

@JsonSerializable()
class Member {
  @JsonKey(name: "member_id")
  int? memberId;
  @JsonKey(name: "member_code")
  String? memberCode;
  @JsonKey(name: "member_name")
  String? memberName;
  @JsonKey(name: "member_name_gj")
  String? memberNameGj;
  @JsonKey(name: "member_name_hi")
  String? memberNameHi;
  @JsonKey(name: "nick_name")
  String? nickName;
  @JsonKey(name: "nick_name_gj")
  String? nickNameGj;
  @JsonKey(name: "nick_name_hi")
  String? nickNameHi;
  @JsonKey(name: "mobile_number")
  String? mobileNumber;
  @JsonKey(name: "alternate_number")
  String? alternateNumber;
  @JsonKey(name: "father_id")
  int? fatherId;
  @JsonKey(name: "father_name")
  String? fatherName;
  @JsonKey(name: "mother_id")
  int? motherId;
  @JsonKey(name: "mother_name")
  String? motherName;
  @JsonKey(name: "spouse_id")
  int? spouseId;
  @JsonKey(name: "spouse_name")
  String? spouseName;
  @JsonKey(name: "gender")
  String? gender;
  @JsonKey(name: "address")
  String? address;
  @JsonKey(name: "state_id")
  int? stateId;
  @JsonKey(name: "state")
  String? state;
  @JsonKey(name: "city_id")
  int? cityId;
  @JsonKey(name: "city")
  String? city;
  @JsonKey(name: "pincode")
  String? pincode;
  @JsonKey(name: "panch_city")
  String? panchCity;
  @JsonKey(name: "email")
  String? email;
  @JsonKey(name: "dob")
  DateTime? dob;
  @JsonKey(name: "age")
  int? age;
  @JsonKey(name: "school_collage_name")
  String? schoolCollageName;
  @JsonKey(name: "hsc")
  String? hsc;
  @JsonKey(name: "ssc")
  String? ssc;
  @JsonKey(name: "martial_status")
  String? martialStatus;
  @JsonKey(name: "graduation")
  String? graduation;
  @JsonKey(name: "occupation")
  String? occupation;
  @JsonKey(name: "company_ferm_name")
  String? companyFermName;
  @JsonKey(name: "experience")
  String? experience;
  @JsonKey(name: "other_business")
  String? otherBusiness;
  @JsonKey(name: "physically_challenged")
  int? physically_challenged;
  @JsonKey(name: "physically_challenged_details")
  String? physicallyChallengedDetails;
  @JsonKey(name: "member_status")
  String? member_status;
  @JsonKey(name: "childrens")
  List<Children>? childrens;

  Member({
    this.memberId,
    this.memberCode,
    this.memberName,
    this.memberNameGj,
    this.memberNameHi,
    this.nickName,
    this.nickNameGj,
    this.nickNameHi,
    this.mobileNumber,
    this.alternateNumber,
    this.fatherId,
    this.fatherName,
    this.motherId,
    this.motherName,
    this.spouseId,
    this.spouseName,
    this.gender,
    this.address,
    this.stateId,
    this.state,
    this.cityId,
    this.city,
    this.pincode,
    this.panchCity,
    this.email,
    this.dob,
    this.age,
    this.martialStatus,
    this.graduation,
    this.occupation,
    this.companyFermName,
    this.otherBusiness,
    this.physicallyChallengedDetails,
    this.member_status,
    this.childrens,
  });

  factory Member.fromJson(Map<String, dynamic> json) => _$MemberFromJson(json);

  Map<String, dynamic> toJson() => _$MemberToJson(this);
}

@JsonSerializable()
class Children {
  @JsonKey(name: "id")
  int? id;
  @JsonKey(name: "name")
  String? name;
  @JsonKey(name: "age")
  int? age;
  @JsonKey(name: "gender")
  String? gender;

  Children({
    this.id,
    this.name,
    this.age,
    this.gender,
  });

  factory Children.fromJson(Map<String, dynamic> json) => _$ChildrenFromJson(json);

  Map<String, dynamic> toJson() => _$ChildrenToJson(this);
}

@JsonSerializable()
class MembersInfo {
  @JsonKey(name: "total_members")
  int? totalMembers;
  @JsonKey(name: "active_members")
  int? activeMembers;
  @JsonKey(name: "naatbahar_members")
  int? naatbaharMembers;

  MembersInfo({
    this.totalMembers,
    this.activeMembers,
    this.naatbaharMembers,
  });

  factory MembersInfo.fromJson(Map<String, dynamic> json) => _$MembersInfoFromJson(json);

  Map<String, dynamic> toJson() => _$MembersInfoToJson(this);
}
