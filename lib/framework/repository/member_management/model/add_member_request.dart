// To parse this JSON data, do
//
//     final addMemberRequest = addMemberRequestFromJson(jsonString);

import 'package:json_annotation/json_annotation.dart';
import 'dart:convert';

part 'add_member_request.g.dart';

AddMemberRequest addMemberRequestFromJson(String str) => AddMemberRequest.fromJson(json.decode(str));

String addMemberRequestToJson(AddMemberRequest data) => json.encode(data.toJson());

@JsonSerializable()
class AddMemberRequest {
  @JsonKey(name: "member_name")
  String? memberName;
  @JsonKey(name: "member_name_hi")
  String? memberNameHi;
  @Json<PERSON>ey(name: "member_name_gj")
  String? memberNameGj;
  @JsonKey(name: "nick_name")
  String? nickName;
  @JsonKey(name: "nick_name_hi")
  String? nickNameHi;
  @Json<PERSON>ey(name: "nick_name_gj")
  String? nickNameGj;
  @JsonKey(name: "photo")
  String? photo;
  @JsonKey(name: "mobile_number")
  String? mobileNumber;
  @Json<PERSON>ey(name: "alternate_mobile_number")
  String? alternateMobileNumber;
  @JsonKey(name: "address")
  String? address;
  @JsonKey(name: "state_id")
  int? stateId;
  @JsonKey(name: "city_id")
  int? cityId;
  @JsonKey(name: "pincode")
  String? pincode;
  @JsonKey(name: "panch_city")
  String? panchCity;
  @JsonKey(name: "father_id")
  int? fatherId;
  @JsonKey(name: "mother_id")
  int? motherId;
  @JsonKey(name: "spouse_id")
  int? spouseId;
  @JsonKey(name: "email")
  String? email;
  @JsonKey(name: "dob")
  DateTime? dob;
  @JsonKey(name: "martial_status")
  String? martialStatus;
  @JsonKey(name: "graduation")
  String? graduation;
  @JsonKey(name: "school_college_name")
  String? schoolCollegeName;
  @JsonKey(name: "hsc")
  String? hsc;
  @JsonKey(name: "ssc")
  String? ssc;
  @JsonKey(name: "occupation")
  String? occupation;
  @JsonKey(name: "company_ferm_name")
  String? companyFermName;
  @JsonKey(name: "experience")
  String? experience;
  @JsonKey(name: "other_business")
  String? otherBusiness;
  @JsonKey(name: "children_ids")
  String? childrenIds;
  @JsonKey(name: "status")
  String? status;
  @JsonKey(name: "is_physically_challenged")
  int? isPhysicallyChallenged;
  @JsonKey(name: "physically_challenged_details")
  String? physicallyChallengedDetails;
  @JsonKey(name: "gender")
  String? gender;

  AddMemberRequest({
    this.memberName,
    this.memberNameHi,
    this.memberNameGj,
    this.nickName,
    this.nickNameHi,
    this.nickNameGj,
    this.photo,
    this.mobileNumber,
    this.alternateMobileNumber,
    this.address,
    this.stateId,
    this.cityId,
    this.pincode,
    this.panchCity,
    this.fatherId,
    this.motherId,
    this.spouseId,
    this.email,
    this.dob,
    this.martialStatus,
    this.graduation,
    this.schoolCollegeName,
    this.hsc,
    this.ssc,
    this.occupation,
    this.companyFermName,
    this.experience,
    this.otherBusiness,
    this.childrenIds,
    this.status,
    this.isPhysicallyChallenged,
    this.physicallyChallengedDetails,
    this.gender,
  });

  factory AddMemberRequest.fromJson(Map<String, dynamic> json) => _$AddMemberRequestFromJson(json);

  Map<String, dynamic> toJson() => _$AddMemberRequestToJson(this);
}
