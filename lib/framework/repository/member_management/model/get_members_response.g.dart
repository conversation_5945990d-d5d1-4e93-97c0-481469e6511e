// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'get_members_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GetMembersResponse _$GetMembersResponseFromJson(Map<String, dynamic> json) =>
    GetMembersResponse(
      status: json['status'] as bool?,
      message: json['message'] as String?,
      data: json['data'] == null
          ? null
          : Data.fromJson(json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$GetMembersResponseToJson(GetMembersResponse instance) =>
    <String, dynamic>{
      'status': instance.status,
      'message': instance.message,
      'data': instance.data,
    };

Data _$DataFromJson(Map<String, dynamic> json) => Data(
      currentPage: (json['current_page'] as num?)?.toInt(),
      perPage: (json['per_page'] as num?)?.toInt(),
      totalPages: (json['total_pages'] as num?)?.toInt(),
      totalItems: (json['total_items'] as num?)?.toInt(),
      members: (json['members'] as List<dynamic>?)
          ?.map((e) => Member.fromJson(e as Map<String, dynamic>))
          .toList(),
      membersInfo: json['members_info'] == null
          ? null
          : MembersInfo.fromJson(json['members_info'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$DataToJson(Data instance) => <String, dynamic>{
      'current_page': instance.currentPage,
      'per_page': instance.perPage,
      'total_pages': instance.totalPages,
      'total_items': instance.totalItems,
      'members': instance.members,
      'members_info': instance.membersInfo,
    };

Member _$MemberFromJson(Map<String, dynamic> json) => Member(
      memberId: (json['member_id'] as num?)?.toInt(),
      memberCode: json['member_code'] as String?,
      memberName: json['member_name'] as String?,
      memberNameGj: json['member_name_gj'] as String?,
      memberNameHi: json['member_name_hi'] as String?,
      nickName: json['nick_name'] as String?,
      nickNameGj: json['nick_name_gj'] as String?,
      nickNameHi: json['nick_name_hi'] as String?,
      mobileNumber: json['mobile_number'] as String?,
      alternateNumber: json['alternate_number'] as String?,
      fatherId: (json['father_id'] as num?)?.toInt(),
      fatherName: json['father_name'] as String?,
      motherId: (json['mother_id'] as num?)?.toInt(),
      motherName: json['mother_name'] as String?,
      spouseId: (json['spouse_id'] as num?)?.toInt(),
      spouseName: json['spouse_name'] as String?,
      gender: json['gender'] as String?,
      address: json['address'] as String?,
      stateId: (json['state_id'] as num?)?.toInt(),
      state: json['state'] as String?,
      cityId: (json['city_id'] as num?)?.toInt(),
      city: json['city'] as String?,
      pincode: json['pincode'] as String?,
      panchCity: json['panch_city'] as String?,
      email: json['email'] as String?,
      dob: json['dob'] == null ? null : DateTime.parse(json['dob'] as String),
      age: (json['age'] as num?)?.toInt(),
      martialStatus: json['martial_status'] as String?,
      graduation: json['graduation'] as String?,
      occupation: json['occupation'] as String?,
      companyFermName: json['company_ferm_name'] as String?,
      otherBusiness: json['other_business'] as String?,
      physicallyChallengedDetails:
          json['physically_challenged_details'] as String?,
      member_status: json['member_status'] as String?,
      childrens: (json['childrens'] as List<dynamic>?)
          ?.map((e) => Children.fromJson(e as Map<String, dynamic>))
          .toList(),
    )
      ..schoolCollageName = json['school_collage_name'] as String?
      ..hsc = json['hsc'] as String?
      ..ssc = json['ssc'] as String?
      ..experience = json['experience'] as String?
      ..physically_challenged =
          (json['physically_challenged'] as num?)?.toInt();

Map<String, dynamic> _$MemberToJson(Member instance) => <String, dynamic>{
      'member_id': instance.memberId,
      'member_code': instance.memberCode,
      'member_name': instance.memberName,
      'member_name_gj': instance.memberNameGj,
      'member_name_hi': instance.memberNameHi,
      'nick_name': instance.nickName,
      'nick_name_gj': instance.nickNameGj,
      'nick_name_hi': instance.nickNameHi,
      'mobile_number': instance.mobileNumber,
      'alternate_number': instance.alternateNumber,
      'father_id': instance.fatherId,
      'father_name': instance.fatherName,
      'mother_id': instance.motherId,
      'mother_name': instance.motherName,
      'spouse_id': instance.spouseId,
      'spouse_name': instance.spouseName,
      'gender': instance.gender,
      'address': instance.address,
      'state_id': instance.stateId,
      'state': instance.state,
      'city_id': instance.cityId,
      'city': instance.city,
      'pincode': instance.pincode,
      'panch_city': instance.panchCity,
      'email': instance.email,
      'dob': instance.dob?.toIso8601String(),
      'age': instance.age,
      'school_collage_name': instance.schoolCollageName,
      'hsc': instance.hsc,
      'ssc': instance.ssc,
      'martial_status': instance.martialStatus,
      'graduation': instance.graduation,
      'occupation': instance.occupation,
      'company_ferm_name': instance.companyFermName,
      'experience': instance.experience,
      'other_business': instance.otherBusiness,
      'physically_challenged': instance.physically_challenged,
      'physically_challenged_details': instance.physicallyChallengedDetails,
      'member_status': instance.member_status,
      'childrens': instance.childrens,
    };

Children _$ChildrenFromJson(Map<String, dynamic> json) => Children(
      id: (json['id'] as num?)?.toInt(),
      name: json['name'] as String?,
      age: (json['age'] as num?)?.toInt(),
      gender: json['gender'] as String?,
    );

Map<String, dynamic> _$ChildrenToJson(Children instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'age': instance.age,
      'gender': instance.gender,
    };

MembersInfo _$MembersInfoFromJson(Map<String, dynamic> json) => MembersInfo(
      totalMembers: (json['total_members'] as num?)?.toInt(),
      activeMembers: (json['active_members'] as num?)?.toInt(),
      naatbaharMembers: (json['naatbahar_members'] as num?)?.toInt(),
    );

Map<String, dynamic> _$MembersInfoToJson(MembersInfo instance) =>
    <String, dynamic>{
      'total_members': instance.totalMembers,
      'active_members': instance.activeMembers,
      'naatbahar_members': instance.naatbaharMembers,
    };
