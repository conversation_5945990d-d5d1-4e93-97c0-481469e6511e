// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'add_member_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AddMemberRequest _$AddMemberRequestFromJson(Map<String, dynamic> json) =>
    AddMemberRequest(
      memberName: json['member_name'] as String?,
      memberNameHi: json['member_name_hi'] as String?,
      memberNameGj: json['member_name_gj'] as String?,
      nickName: json['nick_name'] as String?,
      nickNameHi: json['nick_name_hi'] as String?,
      nickNameGj: json['nick_name_gj'] as String?,
      photo: json['photo'] as String?,
      mobileNumber: json['mobile_number'] as String?,
      alternateMobileNumber: json['alternate_mobile_number'] as String?,
      address: json['address'] as String?,
      stateId: (json['state_id'] as num?)?.toInt(),
      cityId: (json['city_id'] as num?)?.toInt(),
      pincode: json['pincode'] as String?,
      panchCity: json['panch_city'] as String?,
      fatherId: (json['father_id'] as num?)?.toInt(),
      motherId: (json['mother_id'] as num?)?.toInt(),
      spouseId: (json['spouse_id'] as num?)?.toInt(),
      email: json['email'] as String?,
      dob: json['dob'] == null ? null : DateTime.parse(json['dob'] as String),
      martialStatus: json['martial_status'] as String?,
      graduation: json['graduation'] as String?,
      schoolCollegeName: json['school_college_name'] as String?,
      hsc: json['hsc'] as String?,
      ssc: json['ssc'] as String?,
      occupation: json['occupation'] as String?,
      companyFermName: json['company_ferm_name'] as String?,
      experience: json['experience'] as String?,
      otherBusiness: json['other_business'] as String?,
      childrenIds: json['children_ids'] as String?,
      status: json['status'] as String?,
      isPhysicallyChallenged:
          (json['is_physically_challenged'] as num?)?.toInt(),
      physicallyChallengedDetails:
          json['physically_challenged_details'] as String?,
      gender: json['gender'] as String?,
    );

Map<String, dynamic> _$AddMemberRequestToJson(AddMemberRequest instance) =>
    <String, dynamic>{
      'member_name': instance.memberName,
      'member_name_hi': instance.memberNameHi,
      'member_name_gj': instance.memberNameGj,
      'nick_name': instance.nickName,
      'nick_name_hi': instance.nickNameHi,
      'nick_name_gj': instance.nickNameGj,
      'photo': instance.photo,
      'mobile_number': instance.mobileNumber,
      'alternate_mobile_number': instance.alternateMobileNumber,
      'address': instance.address,
      'state_id': instance.stateId,
      'city_id': instance.cityId,
      'pincode': instance.pincode,
      'panch_city': instance.panchCity,
      'father_id': instance.fatherId,
      'mother_id': instance.motherId,
      'spouse_id': instance.spouseId,
      'email': instance.email,
      'dob': instance.dob?.toIso8601String(),
      'martial_status': instance.martialStatus,
      'graduation': instance.graduation,
      'school_college_name': instance.schoolCollegeName,
      'hsc': instance.hsc,
      'ssc': instance.ssc,
      'occupation': instance.occupation,
      'company_ferm_name': instance.companyFermName,
      'experience': instance.experience,
      'other_business': instance.otherBusiness,
      'children_ids': instance.childrenIds,
      'status': instance.status,
      'is_physically_challenged': instance.isPhysicallyChallenged,
      'physically_challenged_details': instance.physicallyChallengedDetails,
      'gender': instance.gender,
    };
