import 'package:ds_admin/framework/repository/member_management/model/add_member_request.dart';
import 'package:ds_admin/framework/repository/member_management/model/update_member_request.dart';

abstract class MemberManagementRepository {
  Future getMembersList(int page, String language, String? memberName, String? cityId, int? minAge, int? maxAge, String? martialStatus);
  Future addMember(AddMemberRequest request);
  Future getStates();
  Future getCities(String? stateId);
  Future updateMember(UpdateMemberRequest request);
  Future deleteMember(int memberId);
}
