class ApiEndPoints {
  /*
  * ----- Api status
  * */
  static const int apiStatus_200 = 200; //success
  static const int apiStatus_201 = 201; //success
  static const int apiStatus_202 = 202; //success for static page
  static const int apiStatus_203 = 203; //success
  static const int apiStatus_205 = 205; // for remaining step 2
  static const int apiStatus_400 = 400; //Invalid data
  static const int apiStatus_401 = 401; //Invalid data
  static const int apiStatus_404 = 404; //Invalid data

  ///Auth
  static String signIn = 'admin/login';

  // Dashboard Module
  static String getDashboardData = 'admin/dashboard';

  // Members Module
  static String getMembers(int page, String language, String? memberName, String? cityId, int? minAge, int? maxAge, String? martialStatus) =>
      'admin/members?page=$page&language=$language&member_name=$memberName&city_id=$cityId&min_age=$minAge&max_age=$maxAge&martial_status=$martialStatus';
  static String addMember = 'admin/member';
  static String updateMember = 'admin/member';
  static String deleteMember = 'admin/member/delete';

  // Events Module
  static String getEvents(int page, String searchText) => 'admin/events?page=$page&search=$searchText';
  static String addEvent = 'admin/event';
  static String updateEvent = 'admin/event';
  static String deleteEvent = 'admin/event/delete';

  // States Module
  static String getStates = 'admin/states';
  static String addState = 'admin/state';
  static String updateState = 'admin/state';
  static String deleteState = 'admin/state/delete';

  // City Module
  static String getCities = 'admin/cities';
  static String addCity = 'admin/city';
  static String updateCity = 'admin/city';
  static String deleteCity = 'admin/city/delete';
  static String getActiveStates = 'admin/states?status=Active';
  static String getActiveCities(String stateId) => 'admin/cities?status=Active&state_id=$stateId';

  //Rules Module
  static String getRules = 'admin/rules';
  static String updateRules = 'admin/rules';

  // About Us Module
  static String getAboutUs = 'admin/about';
  static String updateAboutUs = 'admin/about';
}
