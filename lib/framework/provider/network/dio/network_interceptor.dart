import 'dart:convert';
import 'package:ds_admin/framework/provider/network/dio/common_error_model.dart';
import 'package:ds_admin/framework/provider/network/network.dart';
import 'package:ds_admin/ui/routing/delegate.dart';
import 'package:ds_admin/ui/utils/const/app_constants.dart';
import 'package:ds_admin/ui/utils/widgets/common_dialogs.dart';

bool enableLogoutDialog = true;

InterceptorsWrapper networkInterceptor() {
  CancelToken cancelToken = CancelToken();
  return InterceptorsWrapper(
    onRequest: (request, handler) {
      request.cancelToken = cancelToken;
      handler.next(request);
    },
    onResponse: (response, handler) {
      List<String> whiteListAPIs = [];
      try {
        if ((whiteListAPIs.contains(response.realUri.path)) &&
            (response.data is Map ||
                (response.data is String &&
                    response.data.toString().isNotEmpty))) {
          CommonErrorModel commonModel =
              CommonErrorModel.fromJson(jsonDecode(response.toString()));
          if (commonModel.status != ApiEndPoints.apiStatus_200) {
            if (globalNavigatorKey.currentState?.context != null) {
              showMessageDialog(globalNavigatorKey.currentState!.context,
                  commonModel.message ?? '', null);
              return;
            }
          }
        }
        handler.next(response);
      } catch (e, s) {
        showLog('$stacktrace $s');
        handler.reject(
            DioException(
                requestOptions: response.requestOptions,
                response: response,
                error: const NetworkExceptions.unexpectedError()),
            false);
      }
    },
    onError: (error, handler) {
      final response = error.response;

      if (response?.data != null && (response!.data is Map)) {
        if (response.data != null && response.data.toString().isNotEmpty) {
          // CommonErrorModel commonModel = CommonErrorModel(
          //     status: response.statusCode,
          //     data: null,
          //     message: response.statusMessage);
          if (response.statusCode == ApiEndPoints.apiStatus_401) {
            if (globalNavigatorKey.currentState?.context != null) {
              showLogoutDialog(globalNavigatorKey.currentState!.context);
              return;
            }
          }
        }
      }
      handler.next(error);
    },
  );
}
