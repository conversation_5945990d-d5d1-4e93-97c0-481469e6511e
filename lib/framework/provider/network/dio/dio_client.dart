import 'package:ds_admin/framework/provider/network/network.dart';
import 'package:ds_admin/framework/utils/session.dart';
import 'package:ds_admin/ui/utils/const/app_constants.dart';

class DioClient {
  final Dio _dio;

  DioClient(this._dio);

  /// Set headers and header Authorization
  Map<String, dynamic> getHeader({bool isTokenRequiredInHeader = true}) {
    Map<String, dynamic> headers = {
      accept: headerAccept,
      contentType: headerContentType,
      acceptLanguage: Session.getAppLanguage(),
    };

    if (isTokenRequiredInHeader) {
      ///Authorization Header
      String token = Session.getUserAccessToken();
      int adminId = Session.getLocalData(keyAdminId);
      if (token.isNotEmpty) {
        headers.addAll(
            {"token": token, "adminid": adminId});
      }
    }
    return headers;
  }

  /*
  * ----GET Request
  * */
  Future<dynamic> getRequest(String endPoint, {bool isTokenRequiredInHeader = true}) async {
    try {
      ///Basic Headers
      // Map<String, dynamic> headers = {
      //   'Accept': 'application/json',
      //   'contentType':'application/json',
      //   'Accept-Language': Session.getAppLanguage(),
      // };

      ///Authorization Header

      _dio.options.headers = getHeader(isTokenRequiredInHeader: isTokenRequiredInHeader);
      return await _dio.get(endPoint);
    } catch (e) {
      rethrow;
    }
  }

  /*
  * ----POST Request
  * */
  Future<dynamic> postRequest(
      String endPoint, Map<String, dynamic>? requestBody,
      {bool isTokenRequiredInHeader = true}) async {
    try {
      _dio.options.headers =
          getHeader(isTokenRequiredInHeader: isTokenRequiredInHeader);
      return await _dio.post(endPoint, data: requestBody);
    } catch (e) {
      rethrow;
    }
  }

  /*
  * ----POST Request FormData
  * */
  Future<dynamic> postRequestFormData(
      String endPoint, FormData requestBody, {bool isTokenRequiredInHeader = true}) async {
    try {
      _dio.options.headers = getHeader(isTokenRequiredInHeader: isTokenRequiredInHeader);
      return await _dio.post(endPoint, data: requestBody);
    } catch (e) {
      rethrow;
    }
  }

  /*
  * ----PUT Request
  * */
  Future<dynamic> putRequest(
      String endPoint, Map<String, dynamic>? requestBody, {bool isTokenRequiredInHeader = true}) async {
    try {
      _dio.options.headers = getHeader();
      return await _dio.put(endPoint, data: requestBody);
    } catch (e) {
      rethrow;
    }
  }

  /*
  * ----PUT Request FormData
  * */
  Future<dynamic> putRequestFormData(String endPoint, FormData requestBody, {bool isTokenRequiredInHeader = true}) async {
    try {
      _dio.options.headers = getHeader();
      return await _dio.put(endPoint, data: requestBody);
    } catch (e) {
      rethrow;
    }
  }

  /*
  * ----DELETE Request
  * */
  Future<dynamic> deleteRequest(
      String endPoint, Map<String, dynamic>? requestBody, {bool isTokenRequiredInHeader = true}) async {
    try {
      _dio.options.headers = getHeader();
      return await _dio.delete(endPoint, data: requestBody);
    } catch (e) {
      rethrow;
    }
  }
}
