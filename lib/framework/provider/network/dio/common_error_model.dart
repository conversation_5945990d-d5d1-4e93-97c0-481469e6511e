import 'dart:convert';
import 'package:ds_admin/ui/utils/const/app_constants.dart';

CommonErrorModel commonErrorModelFromJson(String str) {
  try {
    final data = json.decode(str == nullString || str.isEmpty ? emptyString : str);
    return CommonErrorModel.fromJson(data);
  } catch (e) {
    return CommonErrorModel();
  }
}

class CommonErrorModel<T> {
  CommonErrorModel({
    this.message,
    this.data,
    this.status,
  });

  String? message;
  dynamic data;
  int? status;

  factory CommonErrorModel.fromJson(Map<String, dynamic> json) =>
      CommonErrorModel(
        message: json['message'] ?? error,
        data: json['data'],
        status: json['status'] ?? 0,
      );

}
