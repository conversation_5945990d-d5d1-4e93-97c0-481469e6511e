import 'package:ds_admin/framework/utils/extension/string_extension.dart';
import 'package:ds_admin/ui/routing/navigation_stack_item.dart';
import 'package:ds_admin/ui/utils/theme/locale_keys.g.dart';
import 'package:ds_admin/ui/utils/theme/theme.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:injectable/injectable.dart';
import '../../../ui/utils/widgets/admin_menu_item.dart';
import '../../dependency_injection/inject.dart';

final sidebarController = ChangeNotifierProvider(
      (ref) => getIt<SidebarController>(),
);

@injectable
class SidebarController extends ChangeNotifier{

  int _selectedIndex = 0;
  int isDrawerOpen = 0;

  final List<AdminMenuItem> _sideBarItems = [
    AdminMenuItem(
      title: LocaleKeys.keyDashboard.localized,
      route: NavigationStackItem.dashboard(),
      icon: Icons.dashboard,
    ),
    AdminMenuItem(
      title: LocaleKeys.keyMemberManagement.localized,
      route: NavigationStackItem.memberManagement(),
      icon: Icons.people_alt_sharp,
    ),
    AdminMenuItem(
      title: LocaleKeys.keyEventManagement.localized,
      route: NavigationStackItem.eventManagement(),
      icon: Icons.event,
    ),
    AdminMenuItem(
      title: LocaleKeys.keyStateManagement.localized,
      route: NavigationStackItem.stateManagement(),
      icon: Icons.location_city_sharp,
    ),
    AdminMenuItem(
      title: LocaleKeys.keyCityManagement.localized,
      route: NavigationStackItem.cityManagement(),
      icon: Icons.home_work,
    ),
    AdminMenuItem(
      title: LocaleKeys.keyRules.localized,
      route: NavigationStackItem.rulesManagement(),
      icon: Icons.rule_sharp,
    ),
    AdminMenuItem(
      title: LocaleKeys.keyAbout.localized,
      route: NavigationStackItem.aboutManagement(),
      icon: Icons.info,
    ),
  ];


  List<AdminMenuItem> get sideBarItems => _sideBarItems;
  int get selectedIndex => _selectedIndex;

  // void selectMenuItem(int index) {
  //   _selectedIndex = index;
  //   print('Item selected : $_selectedIndex');
  //   notifyListeners();
  // }

  void updateDrawerStatus(int isOpen) {
    isDrawerOpen = isOpen;
    notifyListeners();
  }

}