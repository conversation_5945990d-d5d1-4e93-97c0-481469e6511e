import 'package:ds_admin/framework/repository/common_message_response.dart';
import 'package:ds_admin/framework/repository/event_management/model/add_event_request.dart';
import 'package:ds_admin/framework/repository/event_management/model/edit_event_request.dart';
import 'package:ds_admin/framework/utils/extension/string_extension.dart';
import 'package:ds_admin/main.dart';
import 'package:ds_admin/ui/utils/theme/app_colors.dart';
import 'package:ds_admin/ui/utils/widgets/app_toast.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:html_editor_enhanced/html_editor.dart';
import 'package:injectable/injectable.dart';
import 'package:html/parser.dart' as htmlparser;

import '../../dependency_injection/inject.dart';
import '../../provider/network/network.dart';
import '../../repository/event_management/contract/event_management_repository.dart';
import '../../repository/event_management/model/get_events_response.dart';

final eventManagementController = ChangeNotifierProvider(
  (ref) => getIt<EventManagementController>(),
);

@injectable
class EventManagementController extends ChangeNotifier {
  EventManagementRepository repository;
  EventManagementController(this.repository);

  bool isLoading = false;

  GetEventsResponse eventsResponse = GetEventsResponse();
  List<Event> events = [];
  int currentPage = 1;
  final int eventsPerPage = 5;

  final HtmlEditorController htmlEditorController = HtmlEditorController();

  final formKey = GlobalKey<FormState>();
  final TextEditingController searchController = TextEditingController();
  final TextEditingController titleController = TextEditingController();
  final TextEditingController dateController = TextEditingController();
  DateTime? selectedDate;
  String editorContent = '';
  Uint8List? selectedImageBytes;
  String imagePreviewUrl = '';
  Event? selectedEvent;
  bool isEditing = false;

  ///Dispose Controller
  void disposeController({bool isNotify = false}) {
    isLoading = false;
    titleController.dispose();
    dateController.dispose();
    htmlEditorController.clear();

    if (isNotify) {
      notifyListeners();
    }
  }

  String extractTextFromHtml(String htmlString) {
    final document = htmlparser.parse(htmlString);
    return document.body?.text ?? '';
  }

  void updateEventFormDialog(Event? event) {
    isEditing = event != null;
    selectedEvent = event;

    if (isEditing) {
      titleController.text = event?.eventTitle ?? '';
      selectedDate = event?.eventDate?.getDateTimeObject('yyyy-MM-dd') ?? DateTime.now();
      dateController.text = DateFormat('yyyy-MM-dd').format(event?.eventDate?.getDateTimeObject('yyyy-MM-dd') ?? DateTime.now());
      editorContent = event?.eventDescription ?? '';
      imagePreviewUrl = event?.eventPhoto ?? '';
      // selectedImageBytes = event?.photoBytes;
    } else {
      titleController.clear();
      dateController.clear();
      selectedDate = null;
      editorContent = '';
      imagePreviewUrl = '';
      selectedImageBytes = null;
    }

    notifyListeners();
  }

  void updateDatePicked(DateTime picked) {
    selectedDate = picked;
    dateController.text = DateFormat('yyyy-MM-dd').format(picked);
    notifyListeners();
  }

  void updateImagePicked(Uint8List? image) {
    selectedImageBytes = image;
    imagePreviewUrl = ''; // Clear network image when new image is selected
    notifyListeners();
  }

  Future<void> saveUpdateEventApiCall() async {
    if (isEditing && selectedEvent != null) {
      // Edit existing event
      int eventId = selectedEvent!.eventId!;
      await updateEventApiCall(eventId);
    } else {
      // Add event api call
      await addEventApiCall();
    }
  }

  void updateLoadingStatus(bool value) {
    isLoading = value;
    notifyListeners();
  }

  // Get events
  Future<void> getEventsListApiCall(int page, String searchText) async {
    // Get events api call
    currentPage = page;
    updateLoadingStatus(true);
    ApiResult apiResult = await repository.getEvents(page, searchText);
    apiResult.when(success: (data) {
      updateLoadingStatus(false);
      GetEventsResponse responseModel = data as GetEventsResponse;
      eventsResponse = responseModel;
      if (responseModel.status ?? false) {
        if (page == 1) {
          events.clear();
          events.addAll(responseModel.data?.events ?? []);
        } else {
          events.addAll(responseModel.data?.events ?? []);
        }
      }
      notifyListeners();
    }, failure: (NetworkExceptions error) {
      events.clear();
      updateLoadingStatus(false);
    });
  }

  // Add event
  Future<void> addEventApiCall() async {
    updateLoadingStatus(true);
    String description = await htmlEditorController.getText();
    AddEventRequest event = AddEventRequest(
      eventTitle: titleController.text,
      eventDate: selectedDate?.toString(),
      eventDescription: description.toString(),
    );

    ApiResult apiResult = await repository.addEvent(event, selectedImageBytes);
    apiResult.when(success: (data) {
      updateLoadingStatus(false);
      if (kDebugMode) {
        print('Add event response: $data');
      }
      CommonMessageResponse responseModel = data as CommonMessageResponse;
      if (responseModel.status ?? false) {
        getEventsListApiCall(1, '');
        AppToast.showSnackBar(
          responseModel.message ?? '',
          iconImage: Icons.check_circle,
          iconColor: AppColors.green,
          textColor: AppColors.primary,
          decorationColor: AppColors.greenC7E8A1.withValues(alpha: 0.35),
          snackbarKey.currentState,
              () {
            snackbarKey.currentState?.hideCurrentSnackBar();
          },
        );
      }
      notifyListeners();
    }, failure: (NetworkExceptions error) {
      updateLoadingStatus(false);
      selectedImageBytes = null;
      String errorMsg = NetworkExceptions.getErrorMessage(error);
      if (kDebugMode) {
        print('Add event error: $errorMsg');
      }
      if (errorMsg.contains('"status":')) {
        CommonMessageResponse resp = commonMessageResponseFromJson(errorMsg);
        errorMsg = resp.message ?? 'Something went wrong!';
      }
      AppToast.showSnackBar(
        errorMsg,
        iconImage: Icons.error,
        iconColor: AppColors.redF94008,
        textColor: AppColors.redF94008,
        decorationColor: AppColors.redF94008.withValues(alpha: 0.35),
        snackbarKey.currentState,
            () {
          snackbarKey.currentState?.hideCurrentSnackBar();
        },
      );
    });
  }

  // Update event
  Future<void> updateEventApiCall(int eventId) async {
    updateLoadingStatus(true);
    EditEventRequest event = EditEventRequest(
      eventId: eventId,
      eventTitle: titleController.text,
      eventDate: selectedDate?.toString(),
      eventDescription: await htmlEditorController.getText(),
    );

    ApiResult apiResult = await repository.updateEvent(event, selectedImageBytes);
    apiResult.when(success: (data) {
      selectedImageBytes = null;
      updateLoadingStatus(false);
      CommonMessageResponse responseModel = data as CommonMessageResponse;
      if (responseModel.status ?? false) {
        getEventsListApiCall(1, '');
        AppToast.showSnackBar(
          responseModel.message ?? '',
          iconImage: Icons.check_circle,
          iconColor: AppColors.green,
          textColor: AppColors.primary,
          decorationColor: AppColors.greenC7E8A1.withValues(alpha: 0.35),
          snackbarKey.currentState,
              () {
            snackbarKey.currentState?.hideCurrentSnackBar();
          },
        );
      }
      notifyListeners();
    }, failure: (NetworkExceptions error) {
      updateLoadingStatus(false);
      selectedImageBytes = null;
      String errorMsg = NetworkExceptions.getErrorMessage(error);
      if (errorMsg.contains('"status":')) {
        CommonMessageResponse resp = commonMessageResponseFromJson(errorMsg);
        errorMsg = resp.message ?? 'Something went wrong!';
      }
      AppToast.showSnackBar(
        errorMsg,
        iconImage: Icons.error,
        iconColor: AppColors.redF94008,
        textColor: AppColors.redF94008,
        decorationColor: AppColors.redF94008.withValues(alpha: 0.35),
        snackbarKey.currentState,
            () {
          snackbarKey.currentState?.hideCurrentSnackBar();
        },
      );
    });
  }

  Future<void> deleteEventApiCall(Event event) async {
    updateLoadingStatus(true);
    ApiResult apiResult = await repository.deleteEvent(event.eventId ?? 0);
    apiResult.when(success: (data) {
      updateLoadingStatus(false);
      CommonMessageResponse responseModel = data as CommonMessageResponse;
      if (responseModel.status ?? false) {
        getEventsListApiCall(1, '');
        AppToast.showSnackBar(
          responseModel.message ?? '',
          iconImage: Icons.check_circle,
          iconColor: AppColors.green,
          textColor: AppColors.primary,
          decorationColor: AppColors.greenC7E8A1.withValues(alpha: 0.35),
          snackbarKey.currentState,
              () {
            snackbarKey.currentState?.hideCurrentSnackBar();
          },
        );
      }
      notifyListeners();
    }, failure: (NetworkExceptions error) {
      updateLoadingStatus(false);
      String errorMsg = NetworkExceptions.getErrorMessage(error);
      if (errorMsg.contains('"status":')) {
        CommonMessageResponse resp = commonMessageResponseFromJson(errorMsg);
        errorMsg = resp.message ?? 'Something went wrong!';
      }
      AppToast.showSnackBar(
        errorMsg,
        iconImage: Icons.error,
        iconColor: AppColors.redF94008,
        textColor: AppColors.redF94008,
        decorationColor: AppColors.redF94008.withValues(alpha: 0.35),
        snackbarKey.currentState,
            () {
          snackbarKey.currentState?.hideCurrentSnackBar();
        },
      );
    });
  }


}
