import 'package:ds_admin/framework/provider/network/api_end_points.dart';
import 'package:ds_admin/framework/provider/network/network_exceptions.dart';
import 'package:ds_admin/framework/repository/common_message_response.dart';
import 'package:ds_admin/framework/repository/rules_management/model/get_rules_response.dart';
import 'package:ds_admin/main.dart';
import 'package:ds_admin/ui/utils/theme/theme.dart';
import 'package:ds_admin/ui/utils/widgets/app_toast.dart';
import 'package:ds_admin/ui/utils/widgets/common_dialogs.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:html_editor_enhanced/html_editor.dart';
import 'package:injectable/injectable.dart';

import '../../dependency_injection/inject.dart';
import '../../provider/network/api_result.dart';
import 'package:html/parser.dart' as htmlparser;

import '../../repository/about_management/contract/about_management_repository.dart';
import '../../repository/about_management/model/update_about_request.dart';

final aboutManagementController = ChangeNotifierProvider(
      (ref) => getIt<AboutManagementController>(),
);

@injectable
class AboutManagementController extends ChangeNotifier{
  AboutManagementRepository repository;
  AboutManagementController(this.repository);

  final formKey = GlobalKey<FormState>();

  bool isLoading = false;
  GetRulesResponse rulesResponse = GetRulesResponse();

  TextEditingController ctrlAboutTitle = TextEditingController();
  final HtmlEditorController htmlEditorController = HtmlEditorController();

  ///Dispose Controller
  void disposeController({bool isNotify = false}) {
    isLoading = false;

    if (isNotify) {
      notifyListeners();
    }
  }

  void updateLoadingStatus(bool value) {
    isLoading = value;
    notifyListeners();
  }

  String extractTextFromHtml(String htmlString) {
    final document = htmlparser.parse(htmlString);
    return document.body?.text ?? '';
  }

  // Api Call Get Abouts
  void getAboutApiCall({BuildContext? context}) async {
    updateLoadingStatus(true);
    ApiResult apiResult = await repository.getAbouts();
    apiResult.when(success: (data) {
      GetRulesResponse responseModel = data as GetRulesResponse;
      rulesResponse = responseModel;
      ctrlAboutTitle.text = responseModel.data?.title ?? '';
      updateLoadingStatus(false);
    }, failure: (NetworkExceptions error) {
      updateLoadingStatus(false);
      if (NetworkExceptions.errorStatusCode == ApiEndPoints.apiStatus_401) {
        if(context != null) {
          showLogoutDialog(context);
        }
        return;
      }
    });
  }

  // Api Call Update Rule
  void updateAbout() async{
    updateLoadingStatus(true);
    String aboutHtml = await htmlEditorController.getText();
    UpdateAboutRequest request = UpdateAboutRequest(
      aboutId: rulesResponse.data?.id ?? 0,
      title: ctrlAboutTitle.text,
      description: aboutHtml,
    );
    ApiResult apiResult = await repository.updateAbout(request);
    apiResult.when(success: (data) {
      updateLoadingStatus(false);
      CommonMessageResponse responseModel = data as CommonMessageResponse;
      if (responseModel.status ?? false) {
        getAboutApiCall();
        AppToast.showSnackBar(
          responseModel.message ?? '',
          iconImage: Icons.check_circle,
          iconColor: AppColors.green,
          textColor: AppColors.primary,
          decorationColor: AppColors.greenC7E8A1.withValues(alpha: 0.35),
          snackbarKey.currentState,
              () {
            snackbarKey.currentState?.hideCurrentSnackBar();
          },
        );
      }
    }, failure: (NetworkExceptions error) {
      updateLoadingStatus(false);
      String errorMsg = NetworkExceptions.getErrorMessage(error);
      if (errorMsg.contains('"status":')) {
        CommonMessageResponse resp = commonMessageResponseFromJson(errorMsg);
        errorMsg = resp.message ?? 'Something went wrong!';
      }
      AppToast.showSnackBar(
        errorMsg,
        iconImage: Icons.error,
        iconColor: AppColors.redF94008,
        textColor: AppColors.redF94008,
        decorationColor: AppColors.redF94008.withValues(alpha: 0.35),
        snackbarKey.currentState,
            () {
          snackbarKey.currentState?.hideCurrentSnackBar();
        },
      );
    });
  }

}