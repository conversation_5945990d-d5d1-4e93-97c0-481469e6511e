import 'package:ds_admin/framework/provider/network/network.dart';
import 'package:ds_admin/framework/repository/member_management/contract/member_management_repository.dart';
import 'package:ds_admin/framework/repository/member_management/model/add_member_request.dart';
import 'package:ds_admin/framework/repository/member_management/model/update_member_request.dart';
import 'package:ds_admin/framework/utils/session.dart';
import 'package:ds_admin/ui/utils/widgets/app_toast.dart';
import 'package:ds_admin/ui/utils/widgets/common_dialogs.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:injectable/injectable.dart';

import '../../../main.dart';
import '../../../ui/utils/theme/app_colors.dart';
import '../../dependency_injection/inject.dart';
import '../../repository/city_management/model/get_cities_response.dart';
import '../../repository/common_message_response.dart';
import '../../repository/member_management/model/get_members_response.dart';
import '../../repository/state_management/model/get_states_response.dart';

final memberManagementController = ChangeNotifierProvider(
  (ref) => getIt<MemberManagementController>(),
);

@injectable
class MemberManagementController extends ChangeNotifier {
  MemberManagementRepository repository;
  MemberManagementController(this.repository);

  bool isLoading = false;
  bool isFilterApplied = false;
  List<Member> members = [];
  MembersInfo? membersInfo;
  List<City> cities = [];
  List<States> states = [];
  List<Member> fatherSearchList = [];
  List<Member> motherSearchList = [];
  List<Member> spouseSearchList = [];
  List<Member> childrenSearchList = [];

  TextEditingController ctrlSearchMember = TextEditingController();
  TextEditingController ctrlSearchFather = TextEditingController();
  TextEditingController ctrlSearchMother = TextEditingController();
  TextEditingController ctrlSearchSpouse = TextEditingController();
  TextEditingController ctrlSearchChildren = TextEditingController();

  // filter variables
  int? selectedCity;
  String? selectedMartialStatus;
  int? filterMinAge;
  int? filterMaxAge;
  final TextEditingController minAgeController = TextEditingController();
  final TextEditingController maxAgeController = TextEditingController();

  // Member variables
  final memberFormKey = GlobalKey<FormState>();
  final TextEditingController memberNameController = TextEditingController();
  final TextEditingController memberNameHiController = TextEditingController();
  final TextEditingController memberNameGjController = TextEditingController();
  final TextEditingController nickNameController = TextEditingController();
  final TextEditingController nickNameHiController = TextEditingController();
  final TextEditingController nickNameGjController = TextEditingController();
  final TextEditingController mobileController = TextEditingController();
  final TextEditingController alternateMobileController = TextEditingController();
  final TextEditingController addressController = TextEditingController();
  final TextEditingController pincodeController = TextEditingController();
  final TextEditingController panchCityController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController dobController = TextEditingController();
  final TextEditingController graduationController = TextEditingController();
  final TextEditingController schoolCollegeNameController = TextEditingController();
  final TextEditingController hscController = TextEditingController();
  final TextEditingController sscController = TextEditingController();
  final TextEditingController occupationController = TextEditingController();
  final TextEditingController companyFirmNameController = TextEditingController();
  final TextEditingController experienceController = TextEditingController();
  final TextEditingController otherBusinessController = TextEditingController();
  final TextEditingController physicalChallengedDetailsController = TextEditingController();

  int? selectedStateId;
  int? selectedCityId;
  Member? selectedFather;
  Member? selectedMother;
  Member? selectedSpouse;
  String? selectedMaritalStatus;
  String? selectedStatus;
  String? selectedGender;
  bool isPhysicallyChallenged = false;
  List<Children> selectedChildren = [];

  final List<String> statusOptions = ['Active', 'Inactive', 'Death', 'NaatBahar'];

  final List<String> genderOptions = ['Male', 'Female'];

  // List of martial status
  final List<String> martialStatusList = ['Single', 'Married', 'Divorced', 'Widowed', 'Engaged', 'Vat Kari Che'];

  Future<void> loadMemberData(Member member) async {
    memberNameController.text = member.memberName ?? '';
    memberNameHiController.text = member.memberNameHi ?? '';
    memberNameGjController.text = member.memberNameGj ?? '';
    nickNameController.text = member.nickName ?? '';
    nickNameHiController.text = member.nickNameHi ?? '';
    nickNameGjController.text = member.nickNameGj ?? '';
    mobileController.text = member.mobileNumber ?? '';
    alternateMobileController.text = member.alternateNumber ?? '';
    addressController.text = member.address ?? '';
    pincodeController.text = member.pincode ?? '';
    panchCityController.text = member.panchCity ?? '';
    emailController.text = member.email ?? '';
    dobController.text = DateFormat('yyyy-MM-dd').format(member.dob ?? DateTime.now());
    graduationController.text = member.graduation ?? '';
    schoolCollegeNameController.text = member.schoolCollageName ?? '';
    hscController.text = member.hsc ?? '';
    sscController.text = member.ssc ?? '';
    occupationController.text = member.occupation ?? '';
    companyFirmNameController.text = member.companyFermName ?? '';
    experienceController.text = member.experience ?? '';
    otherBusinessController.text = member.otherBusiness ?? '';
    physicalChallengedDetailsController.text = member.physicallyChallengedDetails ?? '';
    selectedChildren.addAll(member.childrens ?? []);

    await getStatesApiCall();
    await getCities(member.stateId.toString());

    selectedMaritalStatus = member.martialStatus;
    selectedStatus = member.member_status;
    selectedGender = member.gender;
    isPhysicallyChallenged = member.physically_challenged == 1;
    selectedCityId = member.cityId;
    selectedStateId = member.stateId;
    notifyListeners();
  }

  Future<void> selectDate(BuildContext context, {String? initialDate}) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: initialDate == null ? DateTime.now() : DateFormat('yyyy-MM-dd').parse(initialDate),
      firstDate: DateTime(DateTime.now().year - 120),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      dobController.text = DateFormat('yyyy-MM-dd').format(picked);
    }
    notifyListeners();
  }

  ///Dispose Controller
  void disposeController({bool isNotify = false}) {
    isLoading = false;

    if (isNotify) {
      notifyListeners();
    }
  }

  void updateLoadingStatus(bool value) {
    isLoading = value;
    notifyListeners();
  }

  // Api Call Get Members List
  Future<void> getMembersListApiCall(BuildContext context, WidgetRef ref, int page, String language,
      {String? memberName = '', String? cityId = '', int? minAge, int? maxAge, String? martialStatus = ''}) async {
    updateLoadingStatus(true);
    ApiResult apiResult = await repository.getMembersList(page, language, memberName, cityId, minAge, maxAge, martialStatus);
    apiResult.when(success: (data) {
      updateLoadingStatus(false);
      GetMembersResponse responseModel = data as GetMembersResponse;
      if (responseModel.status ?? false) {
        membersInfo = responseModel.data?.membersInfo;
        if (page == 1) {
          members.clear();
          members.addAll(responseModel.data?.members ?? []);
        } else {
          members.addAll(responseModel.data?.members ?? []);
        }
      } else {
        members.clear();
      }
      notifyListeners();
    }, failure: (NetworkExceptions error) {
      updateLoadingStatus(false);
      if (NetworkExceptions.errorStatusCode == ApiEndPoints.apiStatus_401) {
        showLogoutDialog(context);
        return;
      }
    });
  }

  // Api call to get city list
  Future<void> getCities(String? stateId) async {
    updateLoadingStatus(true);
    ApiResult apiResult = await repository.getCities(stateId);
    apiResult.when(success: (data) {
      updateLoadingStatus(false);
      GetCitiesResponse responseModel = data as GetCitiesResponse;
      if (responseModel.status ?? false) {
        cities.clear();
        cities.addAll(responseModel.data?.cities ?? []);
      }
      notifyListeners();
    }, failure: (NetworkExceptions error) {
      updateLoadingStatus(false);
      String errorMsg = NetworkExceptions.getErrorMessage(error);
      if (errorMsg.contains('"status":')) {
        CommonMessageResponse resp = commonMessageResponseFromJson(errorMsg);
        errorMsg = resp.message ?? 'Something went wrong!';
      }
      AppToast.showSnackBar(
        errorMsg,
        iconImage: Icons.error,
        iconColor: AppColors.redF94008,
        textColor: AppColors.redF94008,
        decorationColor: AppColors.redF94008.withValues(alpha: 0.35),
        snackbarKey.currentState,
        () {
          snackbarKey.currentState?.hideCurrentSnackBar();
        },
      );
    });
  }

  // Api Call Get States
  Future<void> getStatesApiCall() async {
    updateLoadingStatus(true);
    ApiResult apiResult = await repository.getStates();
    apiResult.when(success: (data) {
      updateLoadingStatus(false);
      GetStatesResponse responseModel = data as GetStatesResponse;
      if (responseModel.status ?? false) {
        states.clear();
        states.addAll(responseModel.data?.states ?? []);
      }
      notifyListeners();
    }, failure: (NetworkExceptions error) {
      updateLoadingStatus(false);
    });
  }

  // Api Call Delete Member
  Future<void> deleteMemberApiCall(BuildContext context, WidgetRef ref, int memberId) async {
    updateLoadingStatus(true);
    ApiResult apiResult = await repository.deleteMember(memberId);
    apiResult.when(success: (data) {
      updateLoadingStatus(false);
      CommonMessageResponse responseModel = data as CommonMessageResponse;
      AppToast.showSnackBar(
        responseModel.message ?? 'Member deleted successfully!',
        iconImage: Icons.check_circle,
        iconColor: AppColors.green,
        textColor: AppColors.primary,
        decorationColor: AppColors.greenC7E8A1.withValues(alpha: 0.35),
        snackbarKey.currentState,
        () {
          snackbarKey.currentState?.hideCurrentSnackBar();
        },
      );
      getMembersListApiCall(context, ref, 1, Session.getAppLanguage());
    }, failure: (NetworkExceptions error) {
      updateLoadingStatus(false);
      String errorMsg = NetworkExceptions.getErrorMessage(error);
      if (errorMsg.contains('"status":')) {
        CommonMessageResponse resp = commonMessageResponseFromJson(errorMsg);
        errorMsg = resp.message ?? 'Something went wrong!';
      }
      AppToast.showSnackBar(
        errorMsg,
        iconImage: Icons.error,
        iconColor: AppColors.redF94008,
        textColor: AppColors.redF94008,
        decorationColor: AppColors.redF94008.withValues(alpha: 0.35),
        snackbarKey.currentState,
        () {
          snackbarKey.currentState?.hideCurrentSnackBar();
        },
      );
    });
  }

  Future<void> getMembersFatherList(String name) async {
    ApiResult apiResult = await repository.getMembersList(1, Session.getAppLanguage(), name, '', null, null, '');
    apiResult.when(success: (data) {
      updateLoadingStatus(false);
      GetMembersResponse responseModel = data as GetMembersResponse;
      if (responseModel.status ?? false) {
        fatherSearchList.clear();
        fatherSearchList.addAll(responseModel.data?.members ?? []);
      } else {
        fatherSearchList.clear();
      }
      notifyListeners();
    }, failure: (NetworkExceptions error) {
      updateLoadingStatus(false);
    });
  }

  Future<void> getMembersMotherList(String name) async {
    ApiResult apiResult = await repository.getMembersList(1, Session.getAppLanguage(), name, '', null, null, '');
    apiResult.when(success: (data) {
      updateLoadingStatus(false);
      GetMembersResponse responseModel = data as GetMembersResponse;
      if (responseModel.status ?? false) {
        motherSearchList.clear();
        motherSearchList.addAll(responseModel.data?.members ?? []);
      } else {
        motherSearchList.clear();
      }
      notifyListeners();
    }, failure: (NetworkExceptions error) {
      updateLoadingStatus(false);
    });
  }

  Future<void> getMembersSpouseList(String name) async {
    ApiResult apiResult = await repository.getMembersList(1, Session.getAppLanguage(), name, '', null, null, '');
    apiResult.when(success: (data) {
      updateLoadingStatus(false);
      GetMembersResponse responseModel = data as GetMembersResponse;
      if (responseModel.status ?? false) {
        spouseSearchList.clear();
        spouseSearchList.addAll(responseModel.data?.members ?? []);
      } else {
        spouseSearchList.clear();
      }
      notifyListeners();
    }, failure: (NetworkExceptions error) {
      updateLoadingStatus(false);
    });
  }

  Future<void> getMembersChildrenList(String name) async {
    ApiResult apiResult = await repository.getMembersList(1, Session.getAppLanguage(), name, '', null, null, '');
    apiResult.when(success: (data) {
      updateLoadingStatus(false);
      GetMembersResponse responseModel = data as GetMembersResponse;
      if (responseModel.status ?? false) {
        childrenSearchList.clear();
        childrenSearchList.addAll(responseModel.data?.members ?? []);
      } else {
        childrenSearchList.clear();
      }
      notifyListeners();
    }, failure: (NetworkExceptions error) {
      updateLoadingStatus(false);
    });
  }

  String getSelectedChildrenIds() {
    return selectedChildren.map((child) => child.id.toString()).join(',');
  }

  // Api Call Add Member
  Future<void> addMemberApiCall(BuildContext context, WidgetRef ref) async {
    updateLoadingStatus(true);
    if((selectedMaritalStatus ?? '').isEmpty || (selectedStatus ?? '').isEmpty || (selectedGender ?? '').isEmpty || selectedStateId == null || selectedCityId == null) {
      AppToast.showSnackBar(
        "Please fill required fields.",
        iconImage: Icons.error,
        iconColor: AppColors.redF94008,
        textColor: AppColors.redF94008,
        decorationColor: AppColors.redF94008.withValues(alpha: 0.35),
        snackbarKey.currentState,
            () {
          snackbarKey.currentState?.hideCurrentSnackBar();
        },
      );
      return;
    }
    AddMemberRequest request = AddMemberRequest(
        memberName: memberNameController.text,
        memberNameHi: memberNameHiController.text,
        memberNameGj: memberNameGjController.text,
        nickName: nickNameController.text,
        nickNameHi: nickNameHiController.text,
        nickNameGj: nickNameGjController.text,
        mobileNumber: mobileController.text,
        alternateMobileNumber: alternateMobileController.text,
        address: addressController.text,
        pincode: pincodeController.text,
        panchCity: panchCityController.text,
        email: emailController.text,
        dob: dobController.text.isNotEmpty? DateFormat('yyyy-MM-dd').parse(dobController.text) : null,
        martialStatus: selectedMaritalStatus,
        graduation: graduationController.text,
        schoolCollegeName: schoolCollegeNameController.text,
        hsc: hscController.text,
        ssc: sscController.text,
        occupation: occupationController.text,
        companyFermName: companyFirmNameController.text,
        experience: experienceController.text,
        otherBusiness: otherBusinessController.text,
        status: selectedStatus,
        stateId: selectedStateId,
        cityId: selectedCityId,
        fatherId: selectedFather?.memberId,
        motherId: selectedMother?.memberId,
        spouseId: selectedSpouse?.memberId,
        childrenIds: getSelectedChildrenIds(),
        isPhysicallyChallenged: isPhysicallyChallenged ? 1 : 0,
        physicallyChallengedDetails: physicalChallengedDetailsController.text,
        gender: selectedGender);

    ApiResult apiResult = await repository.addMember(request);
    apiResult.when(success: (data) {
      updateLoadingStatus(false);
      getMembersListApiCall(context, ref, 1, Session.getAppLanguage());
      clearMemberData();
      Navigator.of(context).pop();
      CommonMessageResponse responseModel = data as CommonMessageResponse;
      AppToast.showSnackBar(
        responseModel.message ?? 'Member added successfully!',
        iconImage: Icons.check_circle,
        iconColor: AppColors.green,
        textColor: AppColors.primary,
        decorationColor: AppColors.greenC7E8A1.withValues(alpha: 0.35),
        snackbarKey.currentState,
        () {
          snackbarKey.currentState?.hideCurrentSnackBar();
        },
      );
    }, failure: (NetworkExceptions error) {
      updateLoadingStatus(false);
      String errorMsg = NetworkExceptions.getErrorMessage(error);
      if (errorMsg.contains('"status":')) {
        CommonMessageResponse resp = commonMessageResponseFromJson(errorMsg);
        errorMsg = resp.message ?? 'Something went wrong!';
      }
      AppToast.showSnackBar(
        errorMsg,
        iconImage: Icons.error,
        iconColor: AppColors.redF94008,
        textColor: AppColors.redF94008,
        decorationColor: AppColors.redF94008.withValues(alpha: 0.35),
        snackbarKey.currentState,
        () {
          snackbarKey.currentState?.hideCurrentSnackBar();
        },
      );
    });
  }

  // Api Call Update Member
  void updateMemberApiCall(BuildContext context, WidgetRef ref, int memberId) async {
    if((selectedMaritalStatus ?? '').isEmpty || (selectedStatus ?? '').isEmpty || (selectedGender ?? '').isEmpty || selectedStateId == null || selectedCityId == null) {
      AppToast.showSnackBar(
        "Please fill required fields.",
        iconImage: Icons.error,
        iconColor: AppColors.redF94008,
        textColor: AppColors.redF94008,
        decorationColor: AppColors.redF94008.withValues(alpha: 0.35),
        snackbarKey.currentState,
            () {
          snackbarKey.currentState?.hideCurrentSnackBar();
        },
      );
      return;
    }
    updateLoadingStatus(true);
    UpdateMemberRequest request = UpdateMemberRequest(
      memberId: memberId,
      memberName: memberNameController.text,
      memberNameHi: memberNameHiController.text,
      memberNameGj: memberNameGjController.text,
      nickName: nickNameController.text,
      nickNameHi: nickNameHiController.text,
      nickNameGj: nickNameGjController.text,
      mobileNumber: mobileController.text,
      alternateMobileNumber: alternateMobileController.text,
      address: addressController.text,
      pincode: pincodeController.text,
      panchCity: panchCityController.text,
      email: emailController.text,
      dob: DateFormat('yyyy-MM-dd').parse(dobController.text),
      martialStatus: selectedMaritalStatus,
      graduation: graduationController.text,
      schoolCollegeName: schoolCollegeNameController.text,
      hsc: hscController.text,
      ssc: sscController.text,
      occupation: occupationController.text,
      companyFermName: companyFirmNameController.text,
      experience: experienceController.text,
      otherBusiness: otherBusinessController.text,
      status: selectedStatus,
      stateId: selectedStateId,
      cityId: selectedCityId,
      fatherId: selectedFather?.memberId,
      motherId: selectedMother?.memberId,
      spouseId: selectedSpouse?.memberId,
      childrenIds: getSelectedChildrenIds(),
      isPhysicallyChallenged: isPhysicallyChallenged ? 1 : 0,
      physicallyChallengedDetails: physicalChallengedDetailsController.text,
      gender: selectedGender,
    );
    ApiResult apiResult = await repository.updateMember(request);
    apiResult.when(success: (data) {
      updateLoadingStatus(false);
      getMembersListApiCall(context, ref, 1, Session.getAppLanguage());
      clearMemberData();
      CommonMessageResponse responseModel = data as CommonMessageResponse;
      Navigator.of(context).pop();
      AppToast.showSnackBar(
        responseModel.message ?? 'Member added successfully!',
        iconImage: Icons.check_circle,
        iconColor: AppColors.green,
        textColor: AppColors.primary,
        decorationColor: AppColors.greenC7E8A1.withValues(alpha: 0.35),
        snackbarKey.currentState,
        () {
          snackbarKey.currentState?.hideCurrentSnackBar();
        },
      );
    }, failure: (NetworkExceptions error) {
      updateLoadingStatus(false);
      String errorMsg = NetworkExceptions.getErrorMessage(error);
      if (errorMsg.contains('"status":')) {
        CommonMessageResponse resp = commonMessageResponseFromJson(errorMsg);
        errorMsg = resp.message ?? 'Something went wrong!';
      }
      AppToast.showSnackBar(
        errorMsg,
        iconImage: Icons.error,
        iconColor: AppColors.redF94008,
        textColor: AppColors.redF94008,
        decorationColor: AppColors.redF94008.withValues(alpha: 0.35),
        snackbarKey.currentState,
        () {
          snackbarKey.currentState?.hideCurrentSnackBar();
        },
      );
    });
  }

  void updateSelectedCity(int value) {
    selectedCity = value;
    notifyListeners();
  }

  void updateSelectedMartialStatus(String value) {
    selectedMartialStatus = value;
    notifyListeners();
  }

  void updateFilterApplied(bool value) {
    isFilterApplied = value;
    notifyListeners();
  }

  void updateMemberSelectedState(int? value) {
    selectedStateId = value;
    selectedCityId = null;
    if (selectedStateId != null) {
      cities.clear();
      getCities(selectedStateId.toString());
    }
    notifyListeners();
  }

  void updateMemberSelectedCity(int? value) {
    selectedCityId = value;
    notifyListeners();
  }

  void updateMemberFather(Member? value) {
    selectedFather = value;
    notifyListeners();
  }

  void updateMemberMother(Member? value) {
    selectedMother = value;
    notifyListeners();
  }

  void updateMemberSpouse(Member? value) {
    selectedSpouse = value;
    notifyListeners();
  }

  void updateMemberMaritalStatus(String? value) {
    selectedMaritalStatus = value;
    print('selectedMaritalStatus :: $selectedMaritalStatus');
    notifyListeners();
  }

  void updateSelectedMemberStatus(String? value) {
    selectedStatus = value;
    notifyListeners();
  }

  void updatePhysicallyChallenged(bool value) {
    isPhysicallyChallenged = value;
    notifyListeners();
  }

  void updateSelectedGender(String? value) {
    selectedGender = value;
    notifyListeners();
  }

  void addMemberChildren(Member? value) {
    Children child = Children(id: value?.memberId, name: value?.memberName, age: value?.age, gender: value?.gender);

    if (!selectedChildren.any((c) => c.id == child.id)) {
      selectedChildren.add(child);
    }
    notifyListeners();
  }

  void clearMemberData() {
    // Dispose all controllers
    memberNameController.clear();
    memberNameHiController.clear();
    memberNameGjController.clear();
    nickNameController.clear();
    nickNameHiController.clear();
    nickNameGjController.clear();
    mobileController.clear();
    alternateMobileController.clear();
    addressController.clear();
    pincodeController.clear();
    panchCityController.clear();
    emailController.clear();
    dobController.clear();
    graduationController.clear();
    schoolCollegeNameController.clear();
    hscController.clear();
    sscController.clear();
    occupationController.clear();
    companyFirmNameController.clear();
    experienceController.clear();
    otherBusinessController.clear();
    selectedChildren.clear();
    physicalChallengedDetailsController.clear();

    selectedMaritalStatus = null;
    selectedStatus = null;
    selectedGender = null;
    isPhysicallyChallenged = false;
    selectedCityId = null;
    selectedStateId = null;
    selectedFather = null;
    selectedMother = null;
    selectedSpouse = null;
    selectedChildren.clear();
    ctrlSearchFather.clear();
    ctrlSearchMother.clear();
    ctrlSearchSpouse.clear();

    notifyListeners();
  }
}
