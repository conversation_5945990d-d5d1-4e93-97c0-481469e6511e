import 'package:ds_admin/framework/provider/network/api_end_points.dart';
import 'package:ds_admin/framework/provider/network/network_exceptions.dart';
import 'package:ds_admin/framework/repository/common_message_response.dart';
import 'package:ds_admin/framework/repository/dashboard/contract/dashboard_repository.dart';
import 'package:ds_admin/framework/repository/dashboard/model/dashboard_response.dart';
import 'package:ds_admin/main.dart';
import 'package:ds_admin/ui/utils/theme/theme.dart';
import 'package:ds_admin/ui/utils/widgets/app_toast.dart';
import 'package:ds_admin/ui/utils/widgets/common_dialogs.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:injectable/injectable.dart';

import '../../dependency_injection/inject.dart';
import '../../provider/network/api_result.dart';

final dashboardController = ChangeNotifierProvider(
  (ref) => getIt<DashboardController>(),
);

@injectable
class DashboardController extends ChangeNotifier {
  DashboardRepository repository;
  DashboardController(this.repository);

  bool isLoading = false;
  DashboardResponse dashboardResponse = DashboardResponse();

  void updateLoadingStatus(bool value) {
    isLoading = value;
    notifyListeners();
  }

  // API call to get dashboard data
  Future<void> getDashboardData({required BuildContext context}) async {
    updateLoadingStatus(true);
    ApiResult apiResult = await repository.getDashboard();
    apiResult.when(success: (data) {
      updateLoadingStatus(false);
      DashboardResponse responseModel = data as DashboardResponse;
      dashboardResponse = responseModel;
      notifyListeners();
    }, failure: (NetworkExceptions error) {
      updateLoadingStatus(false);
      if (NetworkExceptions.errorStatusCode == ApiEndPoints.apiStatus_401) {
        showLogoutDialog(context);
        return;
      }
      String errorMsg = NetworkExceptions.getErrorMessage(error);
      if (errorMsg.contains('"status":')) {
        CommonMessageResponse resp = commonMessageResponseFromJson(errorMsg);
        errorMsg = resp.message ?? 'Something went wrong!';
      }
      AppToast.showSnackBar(
        errorMsg,
        iconImage: Icons.error,
        iconColor: AppColors.redF94008,
        textColor: AppColors.redF94008,
        decorationColor: AppColors.redF94008.withValues(alpha: 0.35),
        snackbarKey.currentState,
        () {
          snackbarKey.currentState?.hideCurrentSnackBar();
        },
      );
    });
  }
}
