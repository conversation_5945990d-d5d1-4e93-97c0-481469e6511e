import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ds_admin/framework/utils/session.dart';
import '../../../ui/utils/const/app_constants.dart';

final themeModeProvider = ChangeNotifierProvider((ref) => ThemeModeController());

class ThemeModeController extends ChangeNotifier {
  var themeMode =
      (Session.getIsAppThemeDark() ?? false) ? ThemeMode.dark : ThemeMode.light;

  Locale currentLocale = Locale(Session.getAppLanguage());


  void updateThemeMode(ThemeMode themeMode) {
    this.themeMode = themeMode;
    Session.setIsThemeModeDark(themeMode == ThemeMode.dark ? true : false);
    notifyListeners();
  }

  Future<void> updateLocale(String lang) async {
    currentLocale = Locale(lang);
    await globalContext?.setLocale(Locale(lang));
    Session.saveLocalData(keyAppLanguage, lang);
    notifyListeners();
  }

}
