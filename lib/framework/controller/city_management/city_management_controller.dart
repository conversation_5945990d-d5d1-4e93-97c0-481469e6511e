import 'package:ds_admin/framework/provider/network/api_end_points.dart';
import 'package:ds_admin/framework/repository/city_management/contract/city_management_repository.dart';
import 'package:ds_admin/framework/repository/city_management/model/add_city_request.dart';
import 'package:ds_admin/framework/repository/city_management/model/get_cities_response.dart';
import 'package:ds_admin/framework/repository/city_management/model/update_city_request.dart';
import 'package:ds_admin/framework/repository/state_management/model/get_states_response.dart';
import 'package:ds_admin/ui/utils/widgets/common_dialogs.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:injectable/injectable.dart';

import '../../../main.dart';
import '../../../ui/utils/theme/theme.dart';
import '../../../ui/utils/widgets/app_toast.dart';
import '../../dependency_injection/inject.dart';
import '../../provider/network/api_result.dart';
import '../../provider/network/network_exceptions.dart';
import '../../repository/common_message_response.dart';

final cityManagementController = ChangeNotifierProvider(
      (ref) => getIt<CityManagementController>(),
);

@injectable
class CityManagementController extends ChangeNotifier{
  CityManagementRepository repository;
  CityManagementController(this.repository);

  bool isLoading = false;
  GetCitiesResponse citiesResponse = GetCitiesResponse();
  List<City> cities = [];
  List<City> _originalCities = []; // Store original unfiltered cities
  List<States> states = [];

  TextEditingController ctrlCityName = TextEditingController();
  TextEditingController ctrlCityCode = TextEditingController();
  TextEditingController searchController = TextEditingController();
  int ctrlStateId = 0;
  String cityStatus = 'Active';

  // Search, Sort, and Filter properties
  String searchQuery = '';
  String sortOrder = 'asc'; // 'asc' or 'desc'
  String statusFilter = 'All'; // 'All', 'Active', 'Inactive'


  updateLoadingStatus(bool value) {
    isLoading = value;
    notifyListeners();
  }

  disposeController({bool isNotify = false}) {
    isLoading = false;

    if (isNotify) {
      notifyListeners();
    }
  }

  // Api call to get city list
  void getCities({BuildContext? context}) async {
    updateLoadingStatus(true);
    ApiResult apiResult = await repository.getCityList();
    apiResult.when(success: (data) {
      updateLoadingStatus(false);
      GetCitiesResponse responseModel = data as GetCitiesResponse;
      citiesResponse = responseModel;
      if (responseModel.status ?? false) {
        _originalCities.clear();
        _originalCities.addAll(responseModel.data?.cities ?? []);
        _applyFiltersAndSort();
      }
      notifyListeners();
    }, failure: (NetworkExceptions error) {
      updateLoadingStatus(false);
      if (NetworkExceptions.errorStatusCode == ApiEndPoints.apiStatus_401) {
        if(context != null) {
          showLogoutDialog(context);
        }
        return;
      }
      String errorMsg = NetworkExceptions.getErrorMessage(error);
      if (errorMsg.contains('"status":')) {
        CommonMessageResponse resp = commonMessageResponseFromJson(errorMsg);
        errorMsg = resp.message ?? 'Something went wrong!';
      }
      AppToast.showSnackBar(
        errorMsg,
        iconImage: Icons.error,
        iconColor: AppColors.redF94008,
        textColor: AppColors.redF94008,
        decorationColor: AppColors.redF94008.withValues(alpha: 0.35),
        snackbarKey.currentState,
            () {
          snackbarKey.currentState?.hideCurrentSnackBar();
        },
      );

    });
  }

  // Api call to get states
  Future<void> getStates() async {
    // updateLoadingStatus(true);
    ApiResult apiResult = await repository.getActiveStatesList();
    apiResult.when(success: (data) {
      // updateLoadingStatus(false);
      GetStatesResponse responseModel = data as GetStatesResponse;
      if (responseModel.status ?? false) {
        states.clear();
        states.addAll(responseModel.data?.states ?? []);
      }
      notifyListeners();
    }, failure: (NetworkExceptions error) {
      // updateLoadingStatus(false);
    });
  }

  // Api call to add city
  Future<void> addCity() async {
    AddCityRequest request = AddCityRequest(
      cityName: ctrlCityName.text,
      cityCode: ctrlCityCode.text,
      stateId: ctrlStateId,
      status: cityStatus,
    );
    updateLoadingStatus(true);
    ApiResult apiResult = await repository.addCity(request);
    apiResult.when(success: (data) {
      updateLoadingStatus(false);
      CommonMessageResponse responseModel = data as CommonMessageResponse;
      if (responseModel.status ?? false) {
        getCities();
        AppToast.showSnackBar(
          responseModel.message ?? '',
          iconImage: Icons.check_circle,
          iconColor: AppColors.green,
          textColor: AppColors.primary,
          decorationColor: AppColors.greenC7E8A1.withValues(alpha: 0.35),
          snackbarKey.currentState,
              () {
            snackbarKey.currentState?.hideCurrentSnackBar();
          },
        );
      }
      notifyListeners();
    }, failure: (NetworkExceptions error) {
      updateLoadingStatus(false);
      String errorMsg = NetworkExceptions.getErrorMessage(error);
      if (errorMsg.contains('"status":')) {
        CommonMessageResponse resp = commonMessageResponseFromJson(errorMsg);
        errorMsg = resp.message ?? 'Something went wrong!';
      }
      AppToast.showSnackBar(
        errorMsg,
        iconImage: Icons.error,
        iconColor: AppColors.redF94008,
        textColor: AppColors.redF94008,
        decorationColor: AppColors.redF94008.withValues(alpha: 0.35),
        snackbarKey.currentState,
            () {
          snackbarKey.currentState?.hideCurrentSnackBar();
        },
      );
    });
  }

  // Api call to update city
  Future<void> updateCity(int cityId) async {
    UpdateCityRequest request = UpdateCityRequest(
      cityName: ctrlCityName.text,
      cityCode: ctrlCityCode.text,
      stateId: ctrlStateId,
      status: cityStatus,
      cityId: cityId
    );
    updateLoadingStatus(true);
    ApiResult apiResult = await repository.updateCity(request);
    apiResult.when(success: (data) {
      updateLoadingStatus(false);
      CommonMessageResponse responseModel = data as CommonMessageResponse;
      if (responseModel.status ?? false) {
        getCities();
        AppToast.showSnackBar(
          responseModel.message ?? '',
          iconImage: Icons.check_circle,
          iconColor: AppColors.green,
          textColor: AppColors.primary,
          decorationColor: AppColors.greenC7E8A1.withValues(alpha: 0.35),
          snackbarKey.currentState,
              () {
            snackbarKey.currentState?.hideCurrentSnackBar();
          },
        );
      }
      notifyListeners();
    }, failure: (NetworkExceptions error) {
      updateLoadingStatus(false);
      String errorMsg = NetworkExceptions.getErrorMessage(error);
      if (errorMsg.contains('"status":')) {
        CommonMessageResponse resp = commonMessageResponseFromJson(errorMsg);
        errorMsg = resp.message ?? 'Something went wrong!';
      }
      AppToast.showSnackBar(
        errorMsg,
        iconImage: Icons.error,
        iconColor: AppColors.redF94008,
        textColor: AppColors.redF94008,
        decorationColor: AppColors.redF94008.withValues(alpha: 0.35),
        snackbarKey.currentState,
            () {
          snackbarKey.currentState?.hideCurrentSnackBar();
        },
      );
    });

  }

  Future<void> deleteCity(int cityId) async {
    updateLoadingStatus(true);
    ApiResult apiResult = await repository.deleteCity(cityId);
    apiResult.when(success: (data) {
      updateLoadingStatus(false);
      CommonMessageResponse responseModel = data as CommonMessageResponse;
      if (responseModel.status ?? false) {
        getCities();
        AppToast.showSnackBar(
          responseModel.message ?? '',
          iconImage: Icons.check_circle,
          iconColor: AppColors.green,
          textColor: AppColors.primary,
          decorationColor: AppColors.greenC7E8A1.withValues(alpha: 0.35),
          snackbarKey.currentState,
              () {
            snackbarKey.currentState?.hideCurrentSnackBar();
          },
        );
      }
      notifyListeners();
    }, failure: (NetworkExceptions error) {
      updateLoadingStatus(false);
      String errorMsg = NetworkExceptions.getErrorMessage(error);
      if (errorMsg.contains('"status":')) {
        CommonMessageResponse resp = commonMessageResponseFromJson(errorMsg);
        errorMsg = resp.message ?? 'Something went wrong!';
      }
      AppToast.showSnackBar(
        errorMsg,
        iconImage: Icons.error,
        iconColor: AppColors.redF94008,
        textColor: AppColors.redF94008,
        decorationColor: AppColors.redF94008.withValues(alpha: 0.35),
        snackbarKey.currentState,
            () {
          snackbarKey.currentState?.hideCurrentSnackBar();
        },
      );
    });
  }

  void updateCityStatus(String status) {
    cityStatus = status;
    notifyListeners();
  }

  void updateSelectedState(int value) {
    ctrlStateId = value;
    notifyListeners();
  }

  // Search functionality
  void updateSearchQuery(String query) {
    searchQuery = query.toLowerCase();
    _applyFiltersAndSort();
    notifyListeners();
  }

  // Sort functionality
  void updateSortOrder(String order) {
    sortOrder = order;
    _applyFiltersAndSort();
    notifyListeners();
  }

  // Filter functionality
  void updateStatusFilter(String filter) {
    statusFilter = filter;
    _applyFiltersAndSort();
    notifyListeners();
  }

  // Getter for original cities count
  int get originalCitiesCount => _originalCities.length;

  // Get unique status values from cities
  List<String> getAvailableStatuses() {
    Set<String> statuses = _originalCities
        .map((city) => city.status ?? '')
        .where((status) => status.isNotEmpty)
        .toSet();
    List<String> statusList = ['All'];
    statusList.addAll(statuses.toList()..sort());
    return statusList;
  }

  // Apply all filters and sorting
  void _applyFiltersAndSort() {
    List<City> filteredCities = List.from(_originalCities);

    // Apply search filter
    if (searchQuery.isNotEmpty) {
      filteredCities = filteredCities.where((city) {
        final cityName = (city.cityName ?? '').toLowerCase();
        final cityCode = (city.cityCode ?? '').toLowerCase();
        final stateName = (city.stateName ?? '').toLowerCase();

        return cityName.contains(searchQuery) ||
               cityCode.contains(searchQuery) ||
               stateName.contains(searchQuery);
      }).toList();
    }

    // Apply status filter
    if (statusFilter != 'All') {
      filteredCities = filteredCities.where((city) {
        return city.status == statusFilter;
      }).toList();
    }

    // Apply sorting
    filteredCities.sort((a, b) {
      final nameA = (a.cityName ?? '').toLowerCase();
      final nameB = (b.cityName ?? '').toLowerCase();

      if (sortOrder == 'asc') {
        return nameA.compareTo(nameB);
      } else {
        return nameB.compareTo(nameA);
      }
    });

    cities = filteredCities;
  }

  // Clear all filters and reset to original data
  void clearAllFilters() {
    searchQuery = '';
    sortOrder = 'asc';
    statusFilter = 'All';
    searchController.clear();
    _applyFiltersAndSort();
    notifyListeners();
  }

}