import 'package:ds_admin/framework/provider/network/api_end_points.dart';
import 'package:ds_admin/framework/repository/city_management/contract/city_management_repository.dart';
import 'package:ds_admin/framework/repository/city_management/model/add_city_request.dart';
import 'package:ds_admin/framework/repository/city_management/model/get_cities_response.dart';
import 'package:ds_admin/framework/repository/city_management/model/update_city_request.dart';
import 'package:ds_admin/framework/repository/state_management/model/get_states_response.dart';
import 'package:ds_admin/ui/utils/widgets/common_dialogs.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:injectable/injectable.dart';

import '../../../main.dart';
import '../../../ui/utils/theme/theme.dart';
import '../../../ui/utils/widgets/app_toast.dart';
import '../../dependency_injection/inject.dart';
import '../../provider/network/api_result.dart';
import '../../provider/network/network_exceptions.dart';
import '../../repository/common_message_response.dart';

final cityManagementController = ChangeNotifierProvider(
      (ref) => getIt<CityManagementController>(),
);

@injectable
class CityManagementController extends ChangeNotifier{
  CityManagementRepository repository;
  CityManagementController(this.repository);

  bool isLoading = false;
  GetCitiesResponse citiesResponse = GetCitiesResponse();
  List<City> cities = [];
  List<States> states = [];

  TextEditingController ctrlCityName = TextEditingController();
  TextEditingController ctrlCityCode = TextEditingController();
  int ctrlStateId = 0;
  String cityStatus = 'Active';


  updateLoadingStatus(bool value) {
    isLoading = value;
    notifyListeners();
  }

  disposeController({bool isNotify = false}) {
    isLoading = false;

    if (isNotify) {
      notifyListeners();
    }
  }

  // Api call to get city list
  void getCities({BuildContext? context}) async {
    updateLoadingStatus(true);
    ApiResult apiResult = await repository.getCityList();
    apiResult.when(success: (data) {
      updateLoadingStatus(false);
      GetCitiesResponse responseModel = data as GetCitiesResponse;
      citiesResponse = responseModel;
      if (responseModel.status ?? false) {
        cities.clear();
        cities.addAll(responseModel.data?.cities ?? []);
      }
      notifyListeners();
    }, failure: (NetworkExceptions error) {
      updateLoadingStatus(false);
      if (NetworkExceptions.errorStatusCode == ApiEndPoints.apiStatus_401) {
        if(context != null) {
          showLogoutDialog(context);
        }
        return;
      }
      String errorMsg = NetworkExceptions.getErrorMessage(error);
      if (errorMsg.contains('"status":')) {
        CommonMessageResponse resp = commonMessageResponseFromJson(errorMsg);
        errorMsg = resp.message ?? 'Something went wrong!';
      }
      AppToast.showSnackBar(
        errorMsg,
        iconImage: Icons.error,
        iconColor: AppColors.redF94008,
        textColor: AppColors.redF94008,
        decorationColor: AppColors.redF94008.withValues(alpha: 0.35),
        snackbarKey.currentState,
            () {
          snackbarKey.currentState?.hideCurrentSnackBar();
        },
      );

    });
  }

  // Api call to get states
  Future<void> getStates() async {
    // updateLoadingStatus(true);
    ApiResult apiResult = await repository.getActiveStatesList();
    apiResult.when(success: (data) {
      // updateLoadingStatus(false);
      GetStatesResponse responseModel = data as GetStatesResponse;
      if (responseModel.status ?? false) {
        states.clear();
        states.addAll(responseModel.data?.states ?? []);
      }
      notifyListeners();
    }, failure: (NetworkExceptions error) {
      // updateLoadingStatus(false);
    });
  }

  // Api call to add city
  Future<void> addCity() async {
    AddCityRequest request = AddCityRequest(
      cityName: ctrlCityName.text,
      cityCode: ctrlCityCode.text,
      stateId: ctrlStateId,
      status: cityStatus,
    );
    updateLoadingStatus(true);
    ApiResult apiResult = await repository.addCity(request);
    apiResult.when(success: (data) {
      updateLoadingStatus(false);
      CommonMessageResponse responseModel = data as CommonMessageResponse;
      if (responseModel.status ?? false) {
        getCities();
        AppToast.showSnackBar(
          responseModel.message ?? '',
          iconImage: Icons.check_circle,
          iconColor: AppColors.green,
          textColor: AppColors.primary,
          decorationColor: AppColors.greenC7E8A1.withValues(alpha: 0.35),
          snackbarKey.currentState,
              () {
            snackbarKey.currentState?.hideCurrentSnackBar();
          },
        );
      }
      notifyListeners();
    }, failure: (NetworkExceptions error) {
      updateLoadingStatus(false);
      String errorMsg = NetworkExceptions.getErrorMessage(error);
      if (errorMsg.contains('"status":')) {
        CommonMessageResponse resp = commonMessageResponseFromJson(errorMsg);
        errorMsg = resp.message ?? 'Something went wrong!';
      }
      AppToast.showSnackBar(
        errorMsg,
        iconImage: Icons.error,
        iconColor: AppColors.redF94008,
        textColor: AppColors.redF94008,
        decorationColor: AppColors.redF94008.withValues(alpha: 0.35),
        snackbarKey.currentState,
            () {
          snackbarKey.currentState?.hideCurrentSnackBar();
        },
      );
    });
  }

  // Api call to update city
  Future<void> updateCity(int cityId) async {
    UpdateCityRequest request = UpdateCityRequest(
      cityName: ctrlCityName.text,
      cityCode: ctrlCityCode.text,
      stateId: ctrlStateId,
      status: cityStatus,
      cityId: cityId
    );
    updateLoadingStatus(true);
    ApiResult apiResult = await repository.updateCity(request);
    apiResult.when(success: (data) {
      updateLoadingStatus(false);
      CommonMessageResponse responseModel = data as CommonMessageResponse;
      if (responseModel.status ?? false) {
        getCities();
        AppToast.showSnackBar(
          responseModel.message ?? '',
          iconImage: Icons.check_circle,
          iconColor: AppColors.green,
          textColor: AppColors.primary,
          decorationColor: AppColors.greenC7E8A1.withValues(alpha: 0.35),
          snackbarKey.currentState,
              () {
            snackbarKey.currentState?.hideCurrentSnackBar();
          },
        );
      }
      notifyListeners();
    }, failure: (NetworkExceptions error) {
      updateLoadingStatus(false);
      String errorMsg = NetworkExceptions.getErrorMessage(error);
      if (errorMsg.contains('"status":')) {
        CommonMessageResponse resp = commonMessageResponseFromJson(errorMsg);
        errorMsg = resp.message ?? 'Something went wrong!';
      }
      AppToast.showSnackBar(
        errorMsg,
        iconImage: Icons.error,
        iconColor: AppColors.redF94008,
        textColor: AppColors.redF94008,
        decorationColor: AppColors.redF94008.withValues(alpha: 0.35),
        snackbarKey.currentState,
            () {
          snackbarKey.currentState?.hideCurrentSnackBar();
        },
      );
    });

  }

  Future<void> deleteCity(int cityId) async {
    updateLoadingStatus(true);
    ApiResult apiResult = await repository.deleteCity(cityId);
    apiResult.when(success: (data) {
      updateLoadingStatus(false);
      CommonMessageResponse responseModel = data as CommonMessageResponse;
      if (responseModel.status ?? false) {
        getCities();
        AppToast.showSnackBar(
          responseModel.message ?? '',
          iconImage: Icons.check_circle,
          iconColor: AppColors.green,
          textColor: AppColors.primary,
          decorationColor: AppColors.greenC7E8A1.withValues(alpha: 0.35),
          snackbarKey.currentState,
              () {
            snackbarKey.currentState?.hideCurrentSnackBar();
          },
        );
      }
      notifyListeners();
    }, failure: (NetworkExceptions error) {
      updateLoadingStatus(false);
      String errorMsg = NetworkExceptions.getErrorMessage(error);
      if (errorMsg.contains('"status":')) {
        CommonMessageResponse resp = commonMessageResponseFromJson(errorMsg);
        errorMsg = resp.message ?? 'Something went wrong!';
      }
      AppToast.showSnackBar(
        errorMsg,
        iconImage: Icons.error,
        iconColor: AppColors.redF94008,
        textColor: AppColors.redF94008,
        decorationColor: AppColors.redF94008.withValues(alpha: 0.35),
        snackbarKey.currentState,
            () {
          snackbarKey.currentState?.hideCurrentSnackBar();
        },
      );
    });
  }

  void updateCityStatus(String status) {
    cityStatus = status;
    notifyListeners();
  }

  void updateSelectedState(int value) {
    ctrlStateId = value;
    notifyListeners();
  }

}