import 'package:ds_admin/framework/provider/network/api_end_points.dart';
import 'package:ds_admin/framework/provider/network/network_exceptions.dart';
import 'package:ds_admin/framework/repository/common_message_response.dart';
import 'package:ds_admin/framework/repository/rules_management/contract/rules_management_repository.dart';
import 'package:ds_admin/framework/repository/rules_management/model/get_rules_response.dart';
import 'package:ds_admin/framework/repository/rules_management/model/update_rules_request.dart';
import 'package:ds_admin/main.dart';
import 'package:ds_admin/ui/utils/theme/theme.dart';
import 'package:ds_admin/ui/utils/widgets/app_toast.dart';
import 'package:ds_admin/ui/utils/widgets/common_dialogs.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:html_editor_enhanced/html_editor.dart';
import 'package:injectable/injectable.dart';

import '../../dependency_injection/inject.dart';
import '../../provider/network/api_result.dart';
import 'package:html/parser.dart' as htmlparser;

final rulesManagementController = ChangeNotifierProvider(
      (ref) => getIt<RulesManagementController>(),
);

@injectable
class RulesManagementController extends ChangeNotifier{
  RulesManagementRepository repository;
  RulesManagementController(this.repository);

  final formKey = GlobalKey<FormState>();

  bool isLoading = false;
  GetRulesResponse rulesResponse = GetRulesResponse();

  TextEditingController ctrlRuleTitle = TextEditingController();
  final HtmlEditorController htmlEditorController = HtmlEditorController();

  ///Dispose Controller
  void disposeController({bool isNotify = false}) {
    isLoading = false;

    if (isNotify) {
      notifyListeners();
    }
  }

  void updateLoadingStatus(bool value) {
    isLoading = value;
    notifyListeners();
  }

  String extractTextFromHtml(String htmlString) {
    final document = htmlparser.parse(htmlString);
    return document.body?.text ?? '';
  }

  // Api Call Get Rules
  void getRulesApiCall({BuildContext? context}) async {
    updateLoadingStatus(true);
    ApiResult apiResult = await repository.getRules();
    apiResult.when(success: (data) {
      GetRulesResponse responseModel = data as GetRulesResponse;
      rulesResponse = responseModel;
      ctrlRuleTitle.text = responseModel.data?.title ?? '';
      updateLoadingStatus(false);
    }, failure: (NetworkExceptions error) {
      updateLoadingStatus(false);
      if (NetworkExceptions.errorStatusCode == ApiEndPoints.apiStatus_401) {
        if(context != null){
          showLogoutDialog(context);
        }
        return;
      }
    });
  }

  // Api Call Update Rule
  void updateRules() async{
    updateLoadingStatus(true);
    String rulesHtml = await htmlEditorController.getText();
    UpdateRulesRequest request = UpdateRulesRequest(
      ruleId: rulesResponse.data?.id ?? 0,
      title: ctrlRuleTitle.text,
      description: rulesHtml,
    );
    ApiResult apiResult = await repository.updateRule(request);
    apiResult.when(success: (data) {
      updateLoadingStatus(false);
      CommonMessageResponse responseModel = data as CommonMessageResponse;
      if (responseModel.status ?? false) {
        getRulesApiCall();
        AppToast.showSnackBar(
          responseModel.message ?? '',
          iconImage: Icons.check_circle,
          iconColor: AppColors.green,
          textColor: AppColors.primary,
          decorationColor: AppColors.greenC7E8A1.withValues(alpha: 0.35),
          snackbarKey.currentState,
              () {
            snackbarKey.currentState?.hideCurrentSnackBar();
          },
        );
      }
    }, failure: (NetworkExceptions error) {
      updateLoadingStatus(false);
      String errorMsg = NetworkExceptions.getErrorMessage(error);
      if (errorMsg.contains('"status":')) {
        CommonMessageResponse resp = commonMessageResponseFromJson(errorMsg);
        errorMsg = resp.message ?? 'Something went wrong!';
      }
      AppToast.showSnackBar(
        errorMsg,
        iconImage: Icons.error,
        iconColor: AppColors.redF94008,
        textColor: AppColors.redF94008,
        decorationColor: AppColors.redF94008.withValues(alpha: 0.35),
        snackbarKey.currentState,
            () {
          snackbarKey.currentState?.hideCurrentSnackBar();
        },
      );
    });
  }

}