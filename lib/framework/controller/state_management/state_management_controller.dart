import 'package:ds_admin/framework/provider/network/api_end_points.dart';
import 'package:ds_admin/framework/repository/common_message_response.dart';
import 'package:ds_admin/framework/repository/state_management/model/add_state_request.dart';
import 'package:ds_admin/framework/repository/state_management/model/delete_state_request.dart';
import 'package:ds_admin/framework/repository/state_management/model/get_states_response.dart';
import 'package:ds_admin/ui/utils/widgets/common_dialogs.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:injectable/injectable.dart';

import '../../../main.dart';
import '../../../ui/utils/theme/theme.dart';
import '../../../ui/utils/widgets/app_toast.dart';
import '../../dependency_injection/inject.dart';
import '../../provider/network/api_result.dart';
import '../../provider/network/network_exceptions.dart';
import '../../repository/state_management/contract/state_management_repository.dart';
import '../../repository/state_management/model/update_state_request.dart';

final stateManagementController = ChangeNotifierProvider(
      (ref) => getIt<StateManagementController>(),
);

@injectable
class StateManagementController extends ChangeNotifier{
  StateManagementRepository repository;
  StateManagementController(this.repository);

  bool isLoading = false;
  GetStatesResponse statesResponse = GetStatesResponse();
  List<States> states = [];

  TextEditingController ctrlStateName = TextEditingController();
  TextEditingController ctrlStateCode = TextEditingController();
  String stateStatus = 'Active';


  void updateLoadingStatus(bool value) {
    isLoading = value;
    notifyListeners();
  }

  ///Dispose Controller
  void disposeController({bool isNotify = false}) {
    isLoading = false;

    if (isNotify) {
      notifyListeners();
    }
  }

  // Api call to get states
  void getStates({BuildContext? context}) async {
    updateLoadingStatus(true);
    ApiResult apiResult = await repository.getStates();
    apiResult.when(success: (data) {
      updateLoadingStatus(false);
      GetStatesResponse responseModel = data as GetStatesResponse;
      statesResponse = responseModel;
      if (responseModel.status ?? false) {
        states.clear();
        states.addAll(responseModel.data?.states ?? []);
      }
      notifyListeners();
    }, failure: (NetworkExceptions error) {
      updateLoadingStatus(false);
      if (NetworkExceptions.errorStatusCode == ApiEndPoints.apiStatus_401) {
        if(context != null){
          showLogoutDialog(context);
        }
        return;
      }
    });
  }

  // Api call to add state
  void addState() async {
    AddStateRequest request = AddStateRequest(
      stateName: ctrlStateName.text,
      stateCode: ctrlStateCode.text,
      status: stateStatus,
    );
    updateLoadingStatus(true);
    ApiResult apiResult = await repository.addState(request);
    apiResult.when(success: (data) {
      updateLoadingStatus(false);
      CommonMessageResponse responseModel = data as CommonMessageResponse;
      if (responseModel.status ?? false) {
        getStates();
        AppToast.showSnackBar(
          responseModel.message ?? '',
          iconImage: Icons.check_circle,
          iconColor: AppColors.green,
          textColor: AppColors.primary,
          decorationColor: AppColors.greenC7E8A1.withValues(alpha: 0.35),
          snackbarKey.currentState,
              () {
            snackbarKey.currentState?.hideCurrentSnackBar();
          },
        );
      }
      notifyListeners();
    }, failure: (NetworkExceptions error) {
      updateLoadingStatus(false);
      String errorMsg = NetworkExceptions.getErrorMessage(error);
      if (errorMsg.contains('"status":')) {
        CommonMessageResponse resp = commonMessageResponseFromJson(errorMsg);
        errorMsg = resp.message ?? 'Something went wrong!';
      }
      AppToast.showSnackBar(
        errorMsg,
        iconImage: Icons.error,
        iconColor: AppColors.redF94008,
        textColor: AppColors.redF94008,
        decorationColor: AppColors.redF94008.withValues(alpha: 0.35),
        snackbarKey.currentState,
            () {
          snackbarKey.currentState?.hideCurrentSnackBar();
        },
      );
    });
  }

  // Api call to update state
  void updateState(int stateId) async {
    UpdateStateRequest request = UpdateStateRequest(
      stateId: stateId.toString(),
      stateName: ctrlStateName.text,
      stateCode: ctrlStateCode.text,
      status: stateStatus,
    );
    updateLoadingStatus(true);
    ApiResult apiResult = await repository.updateState(request);
    apiResult.when(success: (data) {
      updateLoadingStatus(false);
      CommonMessageResponse responseModel = data as CommonMessageResponse;
      if (responseModel.status ?? false) {
        getStates();
        AppToast.showSnackBar(
          responseModel.message ?? '',
          iconImage: Icons.check_circle,
          iconColor: AppColors.green,
          textColor: AppColors.primary,
          decorationColor: AppColors.greenC7E8A1.withValues(alpha: 0.35),
          snackbarKey.currentState,
              () {
            snackbarKey.currentState?.hideCurrentSnackBar();
          },
        );
      }
      notifyListeners();
    }, failure: (NetworkExceptions error) {
      updateLoadingStatus(false);
      String errorMsg = NetworkExceptions.getErrorMessage(error);
      if (errorMsg.contains('"status":')) {
        CommonMessageResponse resp = commonMessageResponseFromJson(errorMsg);
        errorMsg = resp.message ?? 'Something went wrong!';
      }
      AppToast.showSnackBar(
        errorMsg,
        iconImage: Icons.error,
        iconColor: AppColors.redF94008,
        textColor: AppColors.redF94008,
        decorationColor: AppColors.redF94008.withValues(alpha: 0.35),
        snackbarKey.currentState,
            () {
          snackbarKey.currentState?.hideCurrentSnackBar();
        },
      );
    });
  }

  // Api call to delete state
  void deleteState(int stateId) async{
    updateLoadingStatus(true);
    ApiResult apiResult = await repository.deleteState(stateId);
    apiResult.when(success: (data) {
      updateLoadingStatus(false);
      CommonMessageResponse responseModel = data as CommonMessageResponse;
      if (responseModel.status ?? false) {
        getStates();
        AppToast.showSnackBar(
          responseModel.message ?? '',
          iconImage: Icons.check_circle,
          iconColor: AppColors.green,
          textColor: AppColors.primary,
          decorationColor: AppColors.greenC7E8A1.withValues(alpha: 0.35),
          snackbarKey.currentState,
              () {
            snackbarKey.currentState?.hideCurrentSnackBar();
          },
        );
      }
      notifyListeners();
    }, failure: (NetworkExceptions error) {
      updateLoadingStatus(false);
      String errorMsg = NetworkExceptions.getErrorMessage(error);
      if (errorMsg.contains('"status":')) {
        CommonMessageResponse resp = commonMessageResponseFromJson(errorMsg);
        errorMsg = resp.message ?? 'Something went wrong!';
      }
      AppToast.showSnackBar(
        errorMsg,
        iconImage: Icons.error,
        iconColor: AppColors.redF94008,
        textColor: AppColors.redF94008,
        decorationColor: AppColors.redF94008.withValues(alpha: 0.35),
        snackbarKey.currentState,
            () {
          snackbarKey.currentState?.hideCurrentSnackBar();
        },
      );
    });
  }

  void updateStateStatus(String status) {
    stateStatus = status;
    notifyListeners();
  }

}