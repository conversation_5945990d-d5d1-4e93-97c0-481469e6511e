import 'package:ds_admin/framework/repository/common_message_response.dart';
import 'package:ds_admin/framework/repository/login/contract/login_repository.dart';
import 'package:ds_admin/main.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:injectable/injectable.dart';

import '../../../ui/routing/navigation_stack_item.dart';
import '../../../ui/routing/stack.dart';
import '../../../ui/utils/theme/theme.dart';
import '../../../ui/utils/widgets/app_toast.dart';
import '../../dependency_injection/inject.dart';
import '../../provider/network/api_result.dart';
import '../../provider/network/network_exceptions.dart';
import '../../repository/login/model/admin_login_request.dart';
import '../../repository/login/model/admin_login_response.dart';
import '../../utils/session.dart';

final loginController = ChangeNotifierProvider(
      (ref) => getIt<LoginController>(),
);

@injectable
class LoginController extends ChangeNotifier{
  LoginRepository repository;
  LoginController(this.repository);

  bool isLoading = false;

  ///Dispose Controller
  void disposeController({bool isNotify = false}) {
    isLoading = false;

    if (isNotify) {
      notifyListeners();
    }
  }

  void updateLoadingStatus(bool value) {
    isLoading = value;
    notifyListeners();
  }

  // Api Call Admin Login
  Future<void> loginApiCall(BuildContext context, WidgetRef ref, String username, String password) async {
    updateLoadingStatus(true);
    AdminLoginRequest loginRequest = AdminLoginRequest(
      username: username,
      password: password,
    );

    ApiResult apiResult = await repository.adminLoginApi(loginRequest);
    apiResult.when(success: (data) {
      updateLoadingStatus(false);
      AdminLoginResponse responseModel = data as AdminLoginResponse;
      Session.saveLocalData(keyUserAuthToken, responseModel.data.token);
      Session.saveLocalData(keyAdminId, responseModel.data.id);
      Router.neglect(context, (){
        ref.read(navigationStackController).pushRemove(const NavigationStackItem.dashboard());
      });
    }, failure: (NetworkExceptions error) {
      updateLoadingStatus(false);
      String errorMsg = NetworkExceptions.getErrorMessage(error);
      CommonMessageResponse resp = commonMessageResponseFromJson(errorMsg);
      AppToast.showSnackBar(
        resp.message ?? 'Something went wrong!',
        iconImage: Icons.error,
        iconColor: AppColors.redF94008,
        textColor: AppColors.redF94008,
        decorationColor: AppColors.redF94008.withValues(alpha: 0.35),
        snackbarKey.currentState,
            () {
          snackbarKey.currentState?.hideCurrentSnackBar();
        },
      );
    });
    notifyListeners();
  }

}