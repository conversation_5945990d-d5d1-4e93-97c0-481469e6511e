import 'package:ds_admin/framework/provider/network/dio/dio_client.dart';
import 'package:ds_admin/framework/provider/network/dio/dio_logger.dart';
import 'package:ds_admin/framework/provider/network/dio/network_interceptor.dart';
import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:ds_admin/framework/utils/session.dart';
import 'package:ds_admin/ui/utils/const/app_constants.dart';

@module
abstract class NetworkModule {
  @LazySingleton(env: [production])
  DioClient getProductionDioClient(DioLogger dioLogger) {
    ///Basic Headers
    Map<String, dynamic> headers = {
      accept: headerAccept,
      contentType : headerContentType,
      acceptLanguage : Session.getAppLanguage(),
    };

    ///Authorization Header
    String token = Session.getUserAccessToken();
    if(token.isNotEmpty){
      headers.addAll({authorization: Session.getUserAccessToken()});
    }

    final dio = Dio(
      BaseOptions(
        baseUrl: productionBaseUrl,
        headers: headers,
          sendTimeout: const Duration(milliseconds: 500000),
          connectTimeout: const Duration(milliseconds: 500000),
          receiveTimeout: const Duration(milliseconds: 500000),
      ),
    );
    // dio.interceptors.add(dioLogger);
    final client = DioClient(dio);
    return client;
  }

  @LazySingleton(env: [development])
  DioClient getDebugDioClient(DioLogger dioLogger) {
    ///Basic Headers
    Map<String, dynamic> headers = {
      accept : headerAccept,
      contentType : headerContentType,
      acceptLanguage : Session.getAppLanguage(),
    };

    ///Authorization Header
    String token = Session.getUserAccessToken();
    if(token.isNotEmpty){
      headers.addAll({authorization: Session.getUserAccessToken()});
    }

    final dio = Dio(
      BaseOptions(
        baseUrl: developmentBaseUrl,
          headers: headers,
        sendTimeout: const Duration(milliseconds: 500000),
        connectTimeout: const Duration(milliseconds: 500000),
        receiveTimeout: const Duration(milliseconds: 500000),
      ),
    );
    dio.interceptors.add(dioLogger);
    dio.interceptors.add(networkInterceptor());
    final client = DioClient(dio);
    return client;
  }
}

