// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:ds_admin/framework/controller/about_management/about_management_controller.dart'
    as _i508;
import 'package:ds_admin/framework/controller/city_management/city_management_controller.dart'
    as _i217;
import 'package:ds_admin/framework/controller/dashboard/dashboard_controller.dart'
    as _i829;
import 'package:ds_admin/framework/controller/event_management/event_management_controller.dart'
    as _i276;
import 'package:ds_admin/framework/controller/login/login_controller.dart'
    as _i862;
import 'package:ds_admin/framework/controller/member_management/member_management_controller.dart'
    as _i1021;
import 'package:ds_admin/framework/controller/rules_management/rules_management_controller.dart'
    as _i344;
import 'package:ds_admin/framework/controller/sidebar/sidebar_controller.dart'
    as _i10;
import 'package:ds_admin/framework/controller/state_management/state_management_controller.dart'
    as _i168;
import 'package:ds_admin/framework/dependency_injection/modules/dio_api_client.dart'
    as _i538;
import 'package:ds_admin/framework/dependency_injection/modules/dio_looger_module.dart'
    as _i924;
import 'package:ds_admin/framework/provider/network/dio/dio_client.dart'
    as _i129;
import 'package:ds_admin/framework/provider/network/dio/dio_logger.dart'
    as _i60;
import 'package:ds_admin/framework/provider/network/network.dart' as _i271;
import 'package:ds_admin/framework/repository/about_management/contract/about_management_repository.dart'
    as _i588;
import 'package:ds_admin/framework/repository/about_management/repository/about_management_api_repository.dart'
    as _i955;
import 'package:ds_admin/framework/repository/city_management/contract/city_management_repository.dart'
    as _i676;
import 'package:ds_admin/framework/repository/city_management/repository/city_management_api_repository.dart'
    as _i723;
import 'package:ds_admin/framework/repository/dashboard/contract/dashboard_repository.dart'
    as _i1059;
import 'package:ds_admin/framework/repository/dashboard/repository/dashboard_api_repository.dart'
    as _i962;
import 'package:ds_admin/framework/repository/event_management/contract/event_management_repository.dart'
    as _i935;
import 'package:ds_admin/framework/repository/event_management/repository/event_management_api_repository.dart'
    as _i353;
import 'package:ds_admin/framework/repository/login/contract/login_repository.dart'
    as _i998;
import 'package:ds_admin/framework/repository/login/repository/login_api_repository.dart'
    as _i615;
import 'package:ds_admin/framework/repository/member_management/contract/member_management_repository.dart'
    as _i169;
import 'package:ds_admin/framework/repository/member_management/repository/member_management_api_repository.dart'
    as _i361;
import 'package:ds_admin/framework/repository/rules_management/contract/rules_management_repository.dart'
    as _i895;
import 'package:ds_admin/framework/repository/rules_management/repository/rules_management_api_repository.dart'
    as _i953;
import 'package:ds_admin/framework/repository/state_management/contract/state_management_repository.dart'
    as _i290;
import 'package:ds_admin/framework/repository/state_management/repository/state_management_api_repository.dart'
    as _i941;
import 'package:ds_admin/ui/routing/delegate.dart' as _i207;
import 'package:ds_admin/ui/routing/navigation_stack_item.dart' as _i156;
import 'package:ds_admin/ui/routing/parser.dart' as _i126;
import 'package:ds_admin/ui/routing/stack.dart' as _i406;
import 'package:flutter/material.dart' as _i409;
import 'package:flutter_riverpod/flutter_riverpod.dart' as _i729;
import 'package:get_it/get_it.dart' as _i174;
import 'package:injectable/injectable.dart' as _i526;

const String _development = 'development';
const String _production = 'production';

extension GetItInjectableX on _i174.GetIt {
// initializes the registration of main-scope dependencies inside of GetIt
  _i174.GetIt init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) {
    final gh = _i526.GetItHelper(
      this,
      environment,
      environmentFilter,
    );
    final dioLoggerModule = _$DioLoggerModule();
    final networkModule = _$NetworkModule();
    gh.factory<_i10.SidebarController>(() => _i10.SidebarController());
    gh.factoryParam<_i207.MainRouterDelegate, _i406.NavigationStack, dynamic>((
      stack,
      _,
    ) =>
        _i207.MainRouterDelegate(stack));
    gh.lazySingleton<_i60.DioLogger>(
      () => dioLoggerModule.getDioLogger(),
      registerFor: {
        _development,
        _production,
      },
    );
    gh.factoryParam<_i406.NavigationStack, List<_i156.NavigationStackItem>,
        dynamic>((
      items,
      _,
    ) =>
        _i406.NavigationStack(items));
    gh.factoryParam<_i126.MainRouterInformationParser, _i729.WidgetRef,
        _i409.BuildContext>((
      ref,
      context,
    ) =>
        _i126.MainRouterInformationParser(
          ref,
          context,
        ));
    gh.lazySingleton<_i129.DioClient>(
      () => networkModule.getDebugDioClient(gh<_i60.DioLogger>()),
      registerFor: {_development},
    );
    gh.lazySingleton<_i129.DioClient>(
      () => networkModule.getProductionDioClient(gh<_i60.DioLogger>()),
      registerFor: {_production},
    );
    gh.lazySingleton<_i676.CityManagementRepository>(
      () => _i723.CityManagementApiRepository(gh<_i271.DioClient>()),
      registerFor: {
        _production,
        _development,
      },
    );
    gh.lazySingleton<_i895.RulesManagementRepository>(
      () => _i953.RulesManagementApiRepository(gh<_i129.DioClient>()),
      registerFor: {
        _development,
        _production,
      },
    );
    gh.lazySingleton<_i935.EventManagementRepository>(
      () => _i353.EventManagementApiRepository(gh<_i271.DioClient>()),
      registerFor: {
        _development,
        _production,
      },
    );
    gh.lazySingleton<_i998.LoginRepository>(
      () => _i615.LoginApiRepository(gh<_i271.DioClient>()),
      registerFor: {
        _development,
        _production,
      },
    );
    gh.lazySingleton<_i169.MemberManagementRepository>(
      () => _i361.MemberManagementApiRepository(gh<_i271.DioClient>()),
      registerFor: {
        _development,
        _production,
      },
    );
    gh.factory<_i344.RulesManagementController>(() =>
        _i344.RulesManagementController(gh<_i895.RulesManagementRepository>()));
    gh.lazySingleton<_i588.AboutManagementRepository>(
      () => _i955.AboutManagementApiRepository(gh<_i271.DioClient>()),
      registerFor: {
        _development,
        _production,
      },
    );
    gh.lazySingleton<_i290.StateManagementRepository>(
      () => _i941.StateManagementApiRepository(gh<_i271.DioClient>()),
      registerFor: {
        _development,
        _production,
      },
    );
    gh.factory<_i862.LoginController>(
        () => _i862.LoginController(gh<_i998.LoginRepository>()));
    gh.lazySingleton<_i1059.DashboardRepository>(
      () => _i962.DashboardApiRepository(gh<_i271.DioClient>()),
      registerFor: {
        _development,
        _production,
      },
    );
    gh.factory<_i217.CityManagementController>(() =>
        _i217.CityManagementController(gh<_i676.CityManagementRepository>()));
    gh.factory<_i168.StateManagementController>(() =>
        _i168.StateManagementController(gh<_i290.StateManagementRepository>()));
    gh.factory<_i508.AboutManagementController>(() =>
        _i508.AboutManagementController(gh<_i588.AboutManagementRepository>()));
    gh.factory<_i829.DashboardController>(
        () => _i829.DashboardController(gh<_i1059.DashboardRepository>()));
    gh.factory<_i276.EventManagementController>(() =>
        _i276.EventManagementController(gh<_i935.EventManagementRepository>()));
    gh.factory<_i1021.MemberManagementController>(() =>
        _i1021.MemberManagementController(
            gh<_i169.MemberManagementRepository>()));
    return this;
  }
}

class _$DioLoggerModule extends _i924.DioLoggerModule {}

class _$NetworkModule extends _i538.NetworkModule {}
