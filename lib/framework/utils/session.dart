import 'package:ds_admin/ui/routing/navigation_stack_item.dart';
import 'package:ds_admin/ui/routing/stack.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive/hive.dart';
import 'package:ds_admin/ui/utils/const/app_constants.dart';

const String keyAppLanguage = 'keyAppLanguage';
const String keyIsOnBoardingShowed = 'keyIsOnBoardingShowed';
const String keyUserAuthToken = 'keyUserAuthToken';
const String keyAdminId = 'keyAdminId';
const String keyDeviceId = 'keyDeviceId';
const String keyAppThemeDark = 'keyAppThemeDark';
const String keyUserRefreshToken = 'keyUserRefreshToken';
const String keyDeviceFCMToken = 'keyDeviceFCMToken';

class Session {
  Session._();

  static var userBox = Hive.box(userBoxName);

  static String getUserAccessToken() => (userBox.get(keyUserAuthToken) ?? '');

  static String getAppLanguage() => (userBox.get(keyAppLanguage) ?? 'gu');

  static bool getIsLanguageSelected() => (userBox.get(keyAppLanguage) != null);

  static bool getIsOnBoardingShowed() =>
      (userBox.get(keyIsOnBoardingShowed) ?? false);

  static String getDeviceID() => (userBox.get(keyDeviceId) ?? '');

  static bool? getIsAppThemeDark() => (userBox.get(keyAppThemeDark));

  static String getDeviceFCMToken() =>
      (userBox.get(keyDeviceFCMToken) ?? '');

  ///Save Local Data
  static saveLocalData(String key, value) {
    userBox.put(key, value);
    debugPrint('$savedLocalData \nKey : $key , \nValue : $value', wrapWidth: 500);
  }

  /// get local data
  static getLocalData(String key) {
    return userBox.get(key);
  }

  ///Save Local Data
  static setIsThemeModeDark(value) {
    userBox.put(keyAppThemeDark, value);
  }

  ///Session Logout
  static Future sessionLogout(WidgetRef ref) async {
    // final signInWatch = ref.watch(loginController);
    String appLanguage = getAppLanguage();
    bool isOnBoarding = Session.getIsOnBoardingShowed();
    // String deviceToken = getDeviceFCMToken();
    // String savePassword = getSavePassword();

    await Session.userBox.clear().then((value) {
      // Session.saveLocalData(isGuestUser, true);
      // Session.saveLocalData(showLanguageScreen, false);
      saveLocalData(keyAppLanguage, appLanguage);
      saveLocalData(keyUserAuthToken, '');
      saveLocalData(keyAdminId, '');
      // saveLocalData(KEY_FCM_DEVICE_TOKEN, deviceToken);
      debugPrint('===========================YOU LOGGED OUT FROM THE APP==============================');

      // signInWatch.clearMethod();
      // signInWatch.rememberPassword(savePassword != '' ? true : false);
      // if (savePassword != '') {
      //   signInWatch.updatePasswordControllerValue(savePassword);
      // }
      ref.read(navigationStackController).pushAndRemoveAll(const NavigationStackItem.login());
    });
  }
}
