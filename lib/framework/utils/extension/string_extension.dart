import 'package:easy_localization/easy_localization.dart';
import 'package:ds_admin/ui/utils/const/app_constants.dart';

extension StringExtension on String {
  String get capsFirstLetterOfSentence => '${this[0].toUpperCase()}${substring(1)}';

  String get allInCaps => toUpperCase();

  String get capitalizeFirstLetterOfSentence => split(' ').map((str) => str.capsFirstLetterOfSentence).join(' ');

  String get removeWhiteSpace => replaceAll(' ', '');

  bool get isEmptyString => removeWhiteSpace.isEmpty;

  String get encodedURL => Uri.encodeFull(this);

  bool get isTrue => (this == '1' || toLowerCase() == 't' || toLowerCase() == 'true' || toLowerCase() == 'y' || toLowerCase() == 'yes');

  String get localized => this.tr();

  ///Date Format
  String getCustomDateTimeFormat(String inputFormat, String outputFormat, {bool isCheckPresent = false}) {
    if(this == '' || inputFormat== '' || outputFormat== ''){
      return '';
    }
    DateTime dateTime = getDateTimeObject(inputFormat);
    String value = DateFormat(outputFormat).format(dateTime);
    if(isCheckPresent){
      DateTime currentDateTime = DateTime.now();
      if(dateTime.year == currentDateTime.year && dateTime.month == currentDateTime.month && dateTime.day == currentDateTime.day){
        value = present;
      }
    }
    return value;
  }

  String convertStringToSpecificFormat(String? value, String? format) {
    DateTime dateTime = DateTime.parse(value ?? '');
    String formattedDate = DateFormat(format).format(dateTime);
    return formattedDate;
  }

  DateTime getDateTimeObject(String inputFormat){
    return DateFormat(inputFormat).parse(this);
  }

  String getCustomDateTimeFromUTC(String outputFormat){
    if(this != '' && outputFormat != ''){
      try {
        DateTime temp = DateTime.parse(this).toUtc().toLocal();
        return DateFormat(outputFormat).format(temp);
      } catch (e){
        return DateFormat(outputFormat).format(DateTime.now());
      }
    }
    else{
      return '';
    }
  }

  ///Validations
  bool isPasswordValid() {
    if(length >= 8 && length <= 15) {
      return true;
    } else {
      return false;
    }
  }

  bool isPhoneNumberValid() {
    if (length > 0 && length == 10) {
      return true;
    } else {
      return false;
    }
  }

  bool isEmailValid() {
    Pattern pattern = isEmailValidPattern;
    // RegExp regex = new RegExp(pattern);
    RegExp regex = RegExp(pattern.toString());
    if (!(regex.hasMatch(this))) {
      return false;
    } else {
      return true;
    }
  }

  bool isWebsiteValid() {
    final urlRegExp = RegExp(isWebsiteInvalid);

    if (!(urlRegExp.hasMatch(this))) {
      return false;
    } else {
      return true;
    }
  }

  double parseDouble() {
    return double.tryParse(this) ?? 0.0;
  }

  String get withCurrency => '$currency $this';

}