{"keyBack": "Back", "keyNo": "No", "keyYes": "Yes", "keyOk": "Ok", "keyEdit": "Edit", "keyCancel": "Cancel", "keyAdd": "Add", "keyDabgarSamaj": "<PERSON><PERSON><PERSON>", "keySessionExpired": "Your session is expired! Please login again.", "keyLogin": "<PERSON><PERSON>", "keyWelcomeTO": "Welcome to\n<PERSON><PERSON><PERSON>", "keyWelcome": "Welcome", "keyLoginToAdminPanel": "Login to <PERSON><PERSON><PERSON>", "keyUsername": "Username", "keyPassword": "Password", "keyEnterUsername": "Please enter username.", "keyEnterPassword": "Please enter your password.", "keyDashboard": "Dashboard", "keyMemberManagement": "Member Management", "keyEventManagement": "Event Management", "keyStateManagement": "State Management", "keyCityManagement": "City Management", "keyRules": "Rules", "keyAbout": "About", "keyTotalPopulation": "Population of Samaj", "keyPopulation18PlusMale": "18+ Male Population (sagpan vagar)", "keyPopulation18PlusFemale": "18+ Female Population (sagpan vagar)", "keyTotalCities": "Total Cities", "keyTotalStates": "Total States", "keyTotalMembers": "Total Members", "keyActiveMembers": "Active", "keyNaatbahar": "<PERSON><PERSON>", "keyMembersList": "Members List", "keyAddMember": "Add Member", "keySearchByName": "Search by name", "keyApplyFilter": "Apply Filter", "keyName": "Name", "keyMobile": "Mobile", "keyAge": "Age", "keyCity": "City", "keyAddress": "Address", "keyStatus": "Status", "keyActions": "Actions", "keyEditEvent": "Edit Event", "keyAddNewEvent": "Add New Event", "keyEventTitle": "Event Title", "keyErrEventTitle": "Please enter an event title", "keyEventDate": "Event Date", "keyErrEventDate": "Please select an event date", "keyEventPhoto": "Event Photo", "keyChooseImage": "Choose Image", "keyNewImageSelected": "New image selected", "keyExistingImage": "Using existing imaged", "keyEventDescription": "Event Description", "keyEnterEventDescription": "Enter event description...", "keyUpdateEvent": "Update Event", "keyAddEvent": "Add Event", "keyErrEventDescription": "Please enter event description", "keyEventList": "Event List", "keySearchByTitle": "Search events by title or description", "keyConfirmDelete": "Confirm Delete", "keyConfirmDeleteMsg": "Are you sure you want to delete", "keyDelete": "Delete", "keyImageUnavailable": "Image not available.", "keyNoEventFound": "No events found", "keyAddNewEventMsg": "Add a new event to get started", "keyTryAdjustingSearch": "Try adjusting your search", "keyStatesList": "States List", "keyAddState": "Add State", "keyID": "ID", "keyCode": "Code", "keyNoStatesFound": "No States Found", "keyAddFirstStateMsg": "Add your first state by clicking the \"Add State\" button", "keyAddFirstState": "Add Your First State", "keyEditState": "Edit State", "keyAddNewState": "Add New State", "keyStateName": "State Name", "keyStateNameHint": "Enter state name", "keyErrStateName": "Please enter state name", "keyStateCode": "State Code", "keyStateCodeHint": "Enter state code (e.g., GJ, RJ)", "keyErrStateCode": "Please enter state code", "keyErrStateCodeLength": "State code must be 2 characters", "keyUpdate": "Update", "keyDeleteStateMsg": "Are you sure you want to delete the state", "keyCityList": "City List", "keyAddCity": "Add City", "keyState": "State", "keyNoCityFound": "No City Found", "keyAddFirstCityMsg": "Add your first city by clicking the \"Add City\" button", "keyAddFirstCity": "Add Your First City", "keyEditCity": "Edit City", "keyAddNewCity": "Add New City", "keyCityName": "City Name", "keyCityNameHint": "Enter city name", "keyErrCityName": "Please enter city name", "keyCityCode": "City Code", "keyCityCodeHint": "Enter city code", "keyErrCityCode": "Please enter city code", "keyErrCityCodeLength": "City code must be 2 characters", "keySelectState": "Select State", "keyDeleteCityMsg": "Are you sure you want to delete the city", "keyRulesTitle": "Rules Title", "keyErrRulesTitle": "Please enter rules title", "keyDsRules": "<PERSON><PERSON><PERSON> samaj rules", "keyErrDsRules": "Please enter dabgar samaj rules", "keyAboutTitle": "About Title", "keyErrAboutTitle": "Please enter about title", "keyDsAbout": "About <PERSON><PERSON><PERSON>", "keyErrDsAbout": "Please enter details of dabgar samaj", "keyMinimumAge": "Minimum Age", "keyMaximumAge": "Maximum Age", "keyMartialStatus": "Martial Status", "keySelectCity": "Select City", "keyResetFilter": "Reset Filter", "keyEditMember": "Edit Member", "keyBasicInformation": "Basic Information", "keyMemberName": "Member Name", "keyNickName": "Nickname", "keyContactInformation": "Contact Information", "keyMobileNumber": "Mobile Number", "keyAlternateNumber": "Alternate Mobile Number", "keyLocationInformation": "Location Information", "keyPincode": "Pincode", "keyPanchCity": "Panch City", "keyFamilyInformation": "Family Information", "keyFather": "Father", "keyMother": "Mother", "keySpouse": "Spouse", "keyPersonalInformation": "Personal Information", "keyEmail": "Email", "keyDateofBirth": "Date of Birth", "keyMaritalStatus": "Marital Status", "keyEducationalInformation": "Educational Information", "keyGraduation": "Graduation", "keySchoolCollegeName": "School/College Name", "KeyProfessionalInfo": "Professional Information", "keyOccupation": "Occupation", "keyCompanyFirm": "Company/Firm", "keyExperience": "Experience", "keyOtherBusiness": "Other Business", "keyHSC": "HSC", "keySSC": "SSC", "keyIsPhysicallyChallenged": "Is Physically Challenged", "keyOtherInformation": "Other Information", "keyPhysicallyChallengedDetails": "Physically Challenged Details", "keyGender": "Gender", "keySearchChildrentoadd": "Search Children to add", "keyChildren": "Children", "keySave": "Save"}